# 小野在线导航

一个现代化的个人网址导航平台，支持书签管理、分类组织、快捷访问等功能。

## 🚀 快速启动

### 方式一：使用启动脚本（推荐）

```bash
# 启动所有服务
./start.sh

# 停止所有服务
./stop.sh

# 完整项目管理
./xiaoye.sh start    # 启动所有服务
./xiaoye.sh stop     # 停止所有服务
./xiaoye.sh restart  # 重启所有服务
./xiaoye.sh status   # 查看服务状态
```

### 方式二：手动启动

```bash
# 1. 启动后端 (端口: 3000)
cd backend && npm run start:dev

# 2. 启动用户前端 (端口: 5173)
cd frontend && npm run dev

# 3. 启动管理前端 (端口: 5174)
cd admin-frontend && npm run dev
```

## 📊 访问地址

- **用户前端**: http://localhost:5173 - 主要的书签导航界面
- **管理前端**: http://localhost:5174 - 管理员后台界面
- **后端 API**: http://localhost:3000/api - RESTful API 接口

## 🎯 项目特性

- **用户系统**: 注册/登录、个人设置
- **模块化导航**: 自定义分类、拖拽排序
- **快捷栏**: 可配置的快捷访问区域
- **主题切换**: 明暗主题支持
- **国际化**: 中英文多语言支持
- **响应式设计**: 适配各种设备

## 🛠️ 技术栈

### 前端

- **框架**: React + TypeScript + Vite
- **UI 库**: Material UI (MUI)
- **状态管理**: Recoil
- **路由**: React Router
- **国际化**: LinguiJS
- **动画**: Framer Motion

### 后端

- **框架**: NestJS + TypeScript
- **数据库**: PostgreSQL
- **认证**: JWT + Refresh Token
- **ORM**: TypeORM (计划使用 sqlc)

## 📁 项目结构

```
xiaoye/
├── frontend/          # React 前端应用
├── backend/           # NestJS 后端应用
└── README.md         # 项目说明
```

## 🚀 快速开始

### 前端开发

```bash
cd frontend
npm install
npm run dev
```

### 后端开发

```bash
cd backend
npm install
npm run start:dev
```

## 📋 开发计划

- [x] 项目初始化和环境搭建
- [ ] 前端基础架构搭建
- [ ] 后端基础架构搭建
- [ ] 用户认证系统开发
- [ ] 导航模块核心功能
- [ ] 快捷栏功能实现
- [ ] 主题和国际化
- [ ] UI 优化和响应式设计

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
