# 开发指南

本文档提供小野在线导航项目的详细开发指南。

## 🏗️ 架构概览

### 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户前端       │    │   管理前端       │    │     后端API     │
│  (React+Vite)   │    │  (React+Vite)   │    │   (NestJS)      │
│  Port: 5173     │    │  Port: 5174     │    │  Port: 3000     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   PostgreSQL    │
                    │   Database      │
                    │  Port: 5432     │
                    └─────────────────┘
```

### 技术选型说明

#### 前端技术栈
- **React 18**: 使用最新的 React 特性，包括 Hooks 和 Concurrent Features
- **TypeScript**: 提供类型安全，减少运行时错误
- **Vite**: 快速的开发服务器和构建工具
- **Zustand**: 轻量级状态管理，替代 Redux
- **Material-UI**: Google Material Design 组件库
- **React Router**: 前端路由管理

#### 后端技术栈
- **NestJS**: 企业级 Node.js 框架，支持依赖注入和模块化
- **TypeORM**: 强大的 ORM 框架，支持多种数据库
- **PostgreSQL**: 可靠的关系型数据库
- **JWT**: 无状态认证方案
- **Passport**: 认证中间件

## 🔧 开发环境配置

### 1. 代码编辑器配置

推荐使用 **Visual Studio Code** 并安装以下插件：

```json
{
  "recommendations": [
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-json",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense"
  ]
}
```

### 2. VS Code 工作区设置

创建 `.vscode/settings.json`：

```json
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "files.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/.vite": true
  }
}
```

### 3. Git 配置

创建 `.gitignore`：

```gitignore
# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production builds
dist/
build/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/

# OS
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Runtime
.pids/
*.pid
*.seed
*.pid.lock

# Cache
.vite/
.cache/
```

## 📁 项目结构详解

### 后端结构 (backend/)

```
backend/
├── src/
│   ├── auth/                 # 认证模块
│   │   ├── auth.controller.ts
│   │   ├── auth.service.ts
│   │   ├── auth.module.ts
│   │   ├── dto/              # 数据传输对象
│   │   └── strategies/       # 认证策略
│   ├── bookmarks/            # 书签模块
│   │   ├── bookmarks.controller.ts
│   │   ├── bookmarks.service.ts
│   │   ├── bookmarks.module.ts
│   │   └── dto/
│   ├── categories/           # 分类模块
│   ├── database/             # 数据库配置
│   │   ├── entities/         # 实体定义
│   │   └── migrations/       # 数据库迁移
│   ├── common/               # 通用模块
│   │   ├── decorators/       # 装饰器
│   │   ├── guards/           # 守卫
│   │   └── interceptors/     # 拦截器
│   ├── config/               # 配置文件
│   └── main.ts               # 应用入口
├── test/                     # 测试文件
├── package.json
└── tsconfig.json
```

### 前端结构 (frontend/)

```
frontend/
├── src/
│   ├── components/           # 通用组件
│   │   ├── BookmarkCard.tsx
│   │   ├── CategorySection.tsx
│   │   └── index.ts
│   ├── pages/                # 页面组件
│   │   ├── Home.tsx
│   │   ├── Settings.tsx
│   │   └── index.ts
│   ├── layouts/              # 布局组件
│   │   └── MainLayout.tsx
│   ├── stores/               # Zustand 状态管理
│   │   ├── theme.ts
│   │   ├── bookmarks.ts
│   │   └── locale.ts
│   ├── services/             # API 服务
│   │   └── api.ts
│   ├── themes/               # 主题配置
│   │   └── index.ts
│   ├── utils/                # 工具函数
│   ├── types/                # 类型定义
│   ├── App.tsx               # 应用根组件
│   └── main.tsx              # 应用入口
├── public/                   # 静态资源
├── package.json
└── vite.config.ts
```

## 🔄 开发工作流

### 1. 功能开发流程

1. **创建功能分支**
```bash
git checkout -b feature/new-feature
```

2. **后端开发**
```bash
cd backend
npm run start:dev  # 启动开发服务器
```

3. **前端开发**
```bash
cd frontend
npm run dev  # 启动开发服务器
```

4. **测试**
```bash
npm run test  # 运行测试
```

5. **提交代码**
```bash
git add .
git commit -m "feat: add new feature"
git push origin feature/new-feature
```

### 2. 代码规范

#### TypeScript 规范
- 使用严格的 TypeScript 配置
- 所有函数和变量都要有明确的类型
- 使用接口定义数据结构
- 避免使用 `any` 类型

#### React 组件规范
- 使用函数组件和 Hooks
- 组件名使用 PascalCase
- 文件名与组件名保持一致
- 使用 TypeScript 定义 Props 类型

#### 命名规范
- 文件夹：kebab-case
- 组件：PascalCase
- 函数和变量：camelCase
- 常量：UPPER_SNAKE_CASE

### 3. Git 提交规范

使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

类型说明：
- `feat`: 新功能
- `fix`: 修复 bug
- `docs`: 文档更新
- `style`: 代码格式化
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

## 🧪 测试策略

### 1. 后端测试

```bash
cd backend

# 单元测试
npm run test

# 端到端测试
npm run test:e2e

# 测试覆盖率
npm run test:cov
```

### 2. 前端测试

```bash
cd frontend

# 单元测试
npm run test

# 组件测试
npm run test:component
```

## 🚀 部署流程

### 1. 开发环境部署

```bash
# 启动所有服务
./start.sh

# 或使用管理脚本
./xiaoye.sh start
```

### 2. 生产环境部署

```bash
# 构建所有项目
npm run build:all

# 使用 PM2 部署
pm2 start ecosystem.config.js
```

### 3. Docker 部署

```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d
```

## 🔍 调试技巧

### 1. 后端调试

- 使用 VS Code 的调试功能
- 在 `launch.json` 中配置调试选项
- 使用 `console.log` 和断点调试

### 2. 前端调试

- 使用浏览器开发者工具
- React Developer Tools
- Zustand DevTools

### 3. 数据库调试

```bash
# 连接数据库
psql -h localhost -U xiaoye -d xiaoye_db

# 查看表结构
\dt

# 查看数据
SELECT * FROM bookmarks;
```

## 📚 学习资源

### 官方文档
- [React 官方文档](https://react.dev/)
- [NestJS 官方文档](https://nestjs.com/)
- [TypeORM 官方文档](https://typeorm.io/)
- [Material-UI 官方文档](https://mui.com/)

### 推荐教程
- [TypeScript 深入理解](https://www.typescriptlang.org/docs/)
- [React Hooks 完全指南](https://overreacted.io/a-complete-guide-to-useeffect/)
- [NestJS 实战教程](https://docs.nestjs.com/first-steps)

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

详细贡献指南请参考 [CONTRIBUTING.md](CONTRIBUTING.md)。
