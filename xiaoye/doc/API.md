# API 文档

小野在线导航后端 API 接口文档。

## 📋 基础信息

- **Base URL**: `http://localhost:3000/api`
- **认证方式**: JWT Bearer Token
- **数据格式**: JSON
- **字符编码**: UTF-8

## 🔐 认证接口

### 用户注册

```http
POST /api/auth/register
```

**请求体**:
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "name": "用户名"
}
```

**响应**:
```json
{
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "name": "用户名",
    "role": "user",
    "isActive": true,
    "createdAt": "2024-01-01T00:00:00.000Z"
  },
  "access_token": "jwt_token_here"
}
```

### 用户登录

```http
POST /api/auth/login
```

**请求体**:
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**响应**:
```json
{
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "name": "用户名",
    "role": "user"
  },
  "access_token": "jwt_token_here"
}
```

### 刷新令牌

```http
POST /api/auth/refresh
```

**请求头**:
```
Authorization: Bearer <refresh_token>
```

**响应**:
```json
{
  "access_token": "new_jwt_token_here"
}
```

### 用户登出

```http
POST /api/auth/logout
```

**请求头**:
```
Authorization: Bearer <access_token>
```

## 📚 书签接口

### 获取书签列表

```http
GET /api/bookmarks?userId=<user_id>
```

**查询参数**:
- `userId` (string): 用户ID
- `categoryId` (string, 可选): 分类ID
- `isQuickAccess` (boolean, 可选): 是否为快捷访问

**响应**:
```json
[
  {
    "id": "uuid",
    "title": "Google",
    "url": "https://www.google.com",
    "description": "搜索引擎",
    "icon": "🔍",
    "favicon": "https://www.google.com/favicon.ico",
    "sortOrder": 1,
    "isVisible": true,
    "isQuickAccess": true,
    "userId": "user_uuid",
    "categoryId": "category_uuid",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
]
```

### 获取单个书签

```http
GET /api/bookmarks/:id
```

**路径参数**:
- `id` (string): 书签ID

**响应**:
```json
{
  "id": "uuid",
  "title": "Google",
  "url": "https://www.google.com",
  "description": "搜索引擎",
  "icon": "🔍",
  "favicon": "https://www.google.com/favicon.ico",
  "sortOrder": 1,
  "isVisible": true,
  "isQuickAccess": true,
  "userId": "user_uuid",
  "categoryId": "category_uuid",
  "createdAt": "2024-01-01T00:00:00.000Z",
  "updatedAt": "2024-01-01T00:00:00.000Z"
}
```

### 创建书签

```http
POST /api/bookmarks?userId=<user_id>
```

**请求体**:
```json
{
  "title": "Google",
  "url": "https://www.google.com",
  "description": "搜索引擎",
  "icon": "🔍",
  "categoryId": "category_uuid",
  "isQuickAccess": true
}
```

**响应**:
```json
{
  "id": "uuid",
  "title": "Google",
  "url": "https://www.google.com",
  "description": "搜索引擎",
  "icon": "🔍",
  "favicon": "https://www.google.com/favicon.ico",
  "sortOrder": 1,
  "isVisible": true,
  "isQuickAccess": true,
  "userId": "user_uuid",
  "categoryId": "category_uuid",
  "createdAt": "2024-01-01T00:00:00.000Z",
  "updatedAt": "2024-01-01T00:00:00.000Z"
}
```

### 更新书签

```http
PATCH /api/bookmarks/:id?userId=<user_id>
```

**请求体**:
```json
{
  "title": "Updated Title",
  "description": "Updated description",
  "isQuickAccess": false
}
```

### 删除书签

```http
DELETE /api/bookmarks/:id?userId=<user_id>
```

### 获取快捷访问书签

```http
GET /api/bookmarks/quick-access?userId=<user_id>
```

### 按分类获取书签

```http
GET /api/bookmarks/category/:categoryId?userId=<user_id>
```

## 🗂️ 分类接口

### 获取分类列表

```http
GET /api/categories?userId=<user_id>
```

**响应**:
```json
[
  {
    "id": "uuid",
    "name": "搜索引擎",
    "description": "各种搜索引擎",
    "icon": "🔍",
    "color": "#1976d2",
    "sortOrder": 1,
    "isVisible": true,
    "isQuickAccess": true,
    "userId": "user_uuid",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
]
```

### 获取单个分类

```http
GET /api/categories/:id
```

### 创建分类

```http
POST /api/categories?userId=<user_id>
```

**请求体**:
```json
{
  "name": "搜索引擎",
  "description": "各种搜索引擎",
  "icon": "🔍",
  "color": "#1976d2",
  "isQuickAccess": true
}
```

### 更新分类

```http
PATCH /api/categories/:id?userId=<user_id>
```

### 删除分类

```http
DELETE /api/categories/:id?userId=<user_id>
```

### 获取快捷访问分类

```http
GET /api/categories/quick-access?userId=<user_id>
```

## 📊 错误响应

### 错误格式

```json
{
  "statusCode": 400,
  "message": "错误描述",
  "error": "Bad Request"
}
```

### 常见错误码

| 状态码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突 |
| 500 | 服务器内部错误 |

## 🔧 请求示例

### 使用 curl

```bash
# 用户登录
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# 获取书签列表
curl -X GET "http://localhost:3000/api/bookmarks?userId=user_uuid" \
  -H "Authorization: Bearer <access_token>"

# 创建书签
curl -X POST "http://localhost:3000/api/bookmarks?userId=user_uuid" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <access_token>" \
  -d '{"title":"Google","url":"https://www.google.com","description":"搜索引擎"}'
```

### 使用 JavaScript

```javascript
// 登录
const loginResponse = await fetch('http://localhost:3000/api/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'password123'
  })
});

const { access_token } = await loginResponse.json();

// 获取书签
const bookmarksResponse = await fetch('http://localhost:3000/api/bookmarks?userId=user_uuid', {
  headers: {
    'Authorization': `Bearer ${access_token}`
  }
});

const bookmarks = await bookmarksResponse.json();
```

## 📝 数据模型

### User (用户)

```typescript
interface User {
  id: string;
  email: string;
  name?: string;
  avatar?: string;
  role: 'user' | 'admin';
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}
```

### Bookmark (书签)

```typescript
interface Bookmark {
  id: string;
  title: string;
  url: string;
  description?: string;
  icon?: string;
  favicon?: string;
  sortOrder: number;
  isVisible: boolean;
  isQuickAccess: boolean;
  userId: string;
  categoryId?: string;
  createdAt: string;
  updatedAt: string;
}
```

### Category (分类)

```typescript
interface Category {
  id: string;
  name: string;
  description?: string;
  icon?: string;
  color?: string;
  sortOrder: number;
  isVisible: boolean;
  isQuickAccess: boolean;
  userId: string;
  createdAt: string;
  updatedAt: string;
}
```

## 🔄 版本信息

- **当前版本**: v1.0.0
- **API 版本**: v1
- **最后更新**: 2024-01-01

## 📞 支持

如有问题，请联系开发团队或查看项目文档。
