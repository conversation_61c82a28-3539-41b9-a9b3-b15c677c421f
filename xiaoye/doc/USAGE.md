# 小野在线导航 - 使用说明

## 🚀 快速启动

### 1. 一键启动所有服务

```bash
./start.sh
```

这将启动：
- 后端服务 (http://localhost:3000/api)
- 用户前端 (http://localhost:5173)
- 管理前端 (http://localhost:5174)

### 2. 停止所有服务

```bash
./stop.sh
```

或者按 `Ctrl+C` 停止 start.sh 启动的服务

## 🛠️ 高级管理

### 使用完整管理脚本

```bash
# 查看帮助
./xiaoye.sh

# 启动所有服务
./xiaoye.sh start

# 查看服务状态
./xiaoye.sh status

# 停止所有服务
./xiaoye.sh stop

# 重启所有服务
./xiaoye.sh restart
```

### 单独管理服务

```bash
# 后端服务
./xiaoye.sh backend start
./xiaoye.sh backend stop
./xiaoye.sh backend restart

# 用户前端
./xiaoye.sh frontend start
./xiaoye.sh frontend stop
./xiaoye.sh frontend restart

# 管理前端
./xiaoye.sh admin start
./xiaoye.sh admin stop
./xiaoye.sh admin restart
```

### 查看日志

```bash
# 查看后端日志
./xiaoye.sh logs backend

# 查看用户前端日志
./xiaoye.sh logs frontend

# 查看管理前端日志
./xiaoye.sh logs admin
```

## 📊 服务信息

| 服务 | 端口 | 地址 | 说明 |
|------|------|------|------|
| 后端API | 3000 | http://localhost:3000/api | RESTful API接口 |
| 用户前端 | 5173 | http://localhost:5173 | 主要的书签导航界面 |
| 管理前端 | 5174 | http://localhost:5174 | 管理员后台界面 |

## 🔧 开发模式

所有服务都运行在开发模式下，支持：
- 热重载 (Hot Reload)
- 自动重启
- 开发者工具
- 详细错误信息

## 📝 日志文件

日志文件保存在 `logs/` 目录下：
- `logs/backend.log` - 后端日志
- `logs/frontend.log` - 用户前端日志
- `logs/admin.log` - 管理前端日志

## 🛑 故障排除

### 端口被占用
如果遇到端口被占用的问题：

```bash
# 查看端口占用
lsof -i :3000
lsof -i :5173
lsof -i :5174

# 强制停止
./stop.sh
```

### 依赖问题
如果遇到依赖问题：

```bash
# 重新安装依赖
cd backend && npm install
cd ../frontend && npm install
cd ../admin-frontend && npm install
```

### 数据库连接问题
检查 `backend/.env` 文件中的数据库配置：

```env
DB_HOST=********
DB_PORT=5432
DB_USERNAME=xiaoye
DB_PASSWORD=your_password
DB_DATABASE=xiaoye_db
```

## 💡 使用技巧

1. **推荐使用 `./start.sh`** 进行日常开发
2. **使用 `./xiaoye.sh`** 进行精细化管理
3. **定期查看日志** 了解服务运行状态
4. **使用 `status` 命令** 检查服务是否正常运行
