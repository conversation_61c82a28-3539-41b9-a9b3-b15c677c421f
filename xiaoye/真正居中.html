<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>真正的居中 - 小野在线导航</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            /* 这就是真正的居中 - 占满整个屏幕并居中内容 */
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f5f5f5;
        }
        
        .container {
            /* 固定尺寸的容器，在屏幕正中央 */
            width: 600px;
            background: white;
            border-radius: 8px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            text-align: center;
        }
        
        h1 {
            font-size: 2.5rem;
            font-weight: 300;
            color: #333;
            margin-bottom: 20px;
        }
        
        .subtitle {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 40px;
        }
        
        .demo-box {
            background: #e3f2fd;
            border: 2px solid #1976d2;
            border-radius: 8px;
            padding: 30px;
            margin: 30px 0;
        }
        
        .demo-box h3 {
            color: #1976d2;
            margin-bottom: 15px;
        }
        
        .code-block {
            background: #f5f5f5;
            border-radius: 4px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            color: #333;
            border-left: 4px solid #1976d2;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 30px 0;
            text-align: left;
        }
        
        .wrong, .correct {
            padding: 20px;
            border-radius: 8px;
        }
        
        .wrong {
            background: #ffebee;
            border-left: 4px solid #f44336;
        }
        
        .correct {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
        }
        
        .wrong h4 {
            color: #d32f2f;
            margin-bottom: 10px;
        }
        
        .correct h4 {
            color: #2e7d32;
            margin-bottom: 10px;
        }
        
        .links {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 40px;
        }
        
        .link {
            display: inline-block;
            padding: 12px 24px;
            background: #1976d2;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-weight: 500;
            transition: background 0.3s ease;
        }
        
        .link:hover {
            background: #1565c0;
        }
        
        .admin-link {
            background: #666;
        }
        
        .admin-link:hover {
            background: #555;
        }
        
        .highlight {
            background: #fff3e0;
            border: 1px solid #ff9800;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .highlight h3 {
            color: #f57c00;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 真正的居中</h1>
        <p class="subtitle">现在我明白您要的居中是什么意思了！</p>
        
        <div class="demo-box">
            <h3>这就是真正的居中</h3>
            <p>整个内容区域在屏幕的正中央，就像一个对话框一样</p>
        </div>
        
        <div class="comparison">
            <div class="wrong">
                <h4>❌ 之前的错误理解</h4>
                <p>我以为您要的是元素内部的居中对齐，比如文字居中、按钮居中等。</p>
            </div>
            <div class="correct">
                <h4>✅ 现在的正确理解</h4>
                <p>您要的是整个页面内容在屏幕中央，像弹窗一样的居中效果。</p>
            </div>
        </div>
        
        <div class="highlight">
            <h3>🔧 技术实现</h3>
            <div class="code-block">
/* 真正的居中方法 */<br>
position: fixed;<br>
top: 0; left: 0; right: 0; bottom: 0;<br>
display: flex;<br>
align-items: center;<br>
justify-content: center;
            </div>
        </div>
        
        <div style="background: #f0f8ff; border-radius: 8px; padding: 20px; margin: 20px 0; text-align: left;">
            <h3 style="color: #1976d2; margin-bottom: 15px;">✨ 现在的改进</h3>
            <ul style="color: #666; line-height: 1.8;">
                <li><strong>用户前端</strong> - 900px 宽的内容区域在屏幕正中央</li>
                <li><strong>管理登录</strong> - 400px 宽的登录卡片在屏幕正中央</li>
                <li><strong>管理后台</strong> - 800px 宽的仪表板在屏幕正中央</li>
                <li><strong>用户管理</strong> - 1000px 宽的表格在屏幕正中央</li>
            </ul>
        </div>
        
        <div class="links">
            <a href="http://localhost:5173" class="link" target="_blank">
                📱 用户前端 (真正居中)
            </a>
            <a href="http://localhost:5174" class="link admin-link" target="_blank">
                🔧 管理后台 (真正居中)
            </a>
        </div>
        
        <div style="margin-top: 30px; padding: 15px; background: #e8f5e8; border-radius: 8px; border-left: 4px solid #4caf50;">
            <p style="color: #2e7d32; font-weight: 500;">
                ✅ 现在所有页面都实现了真正的居中效果！
            </p>
        </div>
    </div>
</body>
</html>
