# 小野在线导航

一个现代化的个人网址导航平台，支持书签管理、分类组织、快捷访问等功能。

## 📋 目录

- [功能特性](#-功能特性)
- [技术栈](#-技术栈)
- [系统要求](#-系统要求)
- [安装指南](#-安装指南)
- [快速启动](#-快速启动)
- [访问地址](#-访问地址)
- [项目结构](#-项目结构)
- [开发指南](#-开发指南)
- [故障排除](#-故障排除)

## ✨ 功能特性

- 📚 **书签管理** - 添加、编辑、删除书签，支持图标和描述
- 🗂️ **分类组织** - 自定义分类，支持图标、颜色和排序
- ⚡ **快捷访问** - 常用书签和分类的快速访问
- 🎨 **主题切换** - 明暗主题支持，自动保存用户偏好
- 🌍 **国际化** - 中英文双语支持
- 📱 **响应式设计** - 适配桌面、平板、手机等各种设备
- 👥 **用户管理** - 多用户支持，角色权限控制
- 🔐 **安全认证** - JWT 认证，密码加密存储
- ⚙️ **管理后台** - 完整的管理员后台界面

## 🛠️ 技术栈

### 前端技术

- **React 18** - 现代化的前端框架
- **TypeScript** - 类型安全的 JavaScript
- **Vite** - 快速的构建工具
- **Material-UI (MUI)** - Google Material Design 组件库
- **Zustand** - 轻量级状态管理
- **React Router** - 前端路由管理
- **Axios** - HTTP 客户端

### 后端技术

- **NestJS** - 企业级 Node.js 框架
- **TypeScript** - 类型安全的服务端开发
- **TypeORM** - 强大的 ORM 框架
- **PostgreSQL** - 可靠的关系型数据库
- **JWT** - JSON Web Token 认证
- **Passport** - 认证中间件
- **bcrypt** - 密码加密

## 💻 系统要求

- **Node.js** >= 18.0.0
- **npm** >= 9.0.0 或 **yarn** >= 1.22.0
- **PostgreSQL** >= 13.0
- **Git** >= 2.0.0

## 📦 安装指南

### 1. 克隆项目

```bash
git clone <repository-url>
cd xiaoye
```

### 2. 安装依赖

```bash
# 安装后端依赖
cd backend
npm install

# 安装用户前端依赖
cd ../frontend
npm install

# 安装管理前端依赖
cd ../admin-frontend
npm install

# 返回项目根目录
cd ..
```

### 3. 数据库配置

#### 3.1 安装 PostgreSQL

**macOS (使用 Homebrew):**

```bash
brew install postgresql
brew services start postgresql
```

**Ubuntu/Debian:**

```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

**Windows:**
下载并安装 [PostgreSQL 官方安装包](https://www.postgresql.org/download/windows/)

#### 3.2 创建数据库

```bash
# 连接到 PostgreSQL
psql -U postgres

# 创建数据库和用户
CREATE DATABASE xiaoye_db;
CREATE USER xiaoye WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE xiaoye_db TO xiaoye;
\q
```

#### 3.3 配置环境变量

在 `backend` 目录下创建 `.env` 文件：

```bash
cd backend
cp .env.example .env
```

编辑 `.env` 文件：

```env
# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=xiaoye
DB_PASSWORD=your_password
DB_DATABASE=xiaoye_db

# JWT 配置
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d

# 应用配置
PORT=3000
NODE_ENV=development
```

### 4. 初始化数据库

```bash
cd backend
npm run start:dev
```

首次启动时，TypeORM 会自动创建所需的数据表。

## 🚀 快速启动

### 方式一：使用启动脚本（推荐）

```bash
# 给脚本添加执行权限（首次运行）
chmod +x xiaoye.sh start.sh stop.sh

# 启动所有服务
./start.sh

# 或使用完整管理脚本
./xiaoye.sh start    # 启动所有服务
./xiaoye.sh stop     # 停止所有服务
./xiaoye.sh restart  # 重启所有服务
./xiaoye.sh status   # 查看服务状态
```

### 方式二：手动启动

```bash
# 1. 启动后端服务 (端口: 3000)
cd backend
npm run start:dev

# 2. 启动用户前端 (端口: 5173)
cd ../frontend
npm run dev

# 3. 启动管理前端 (端口: 5174)
cd ../admin-frontend
npm run dev
```

## 📊 访问地址

启动成功后，您可以访问以下地址：

- **用户前端**: http://localhost:5173 - 主要的书签导航界面
- **管理前端**: http://localhost:5174 - 管理员后台界面
- **后端 API**: http://localhost:3000/api - RESTful API 接口

## 📁 项目结构

```
xiaoye/
├── backend/                 # 后端服务 (NestJS)
│   ├── src/
│   │   ├── auth/           # 认证模块
│   │   ├── bookmarks/      # 书签模块
│   │   ├── categories/     # 分类模块
│   │   ├── database/       # 数据库配置
│   │   └── main.ts         # 应用入口
│   ├── package.json
│   └── .env.example
├── frontend/               # 用户前端 (React + Vite)
│   ├── src/
│   │   ├── components/     # 通用组件
│   │   ├── pages/          # 页面组件
│   │   ├── stores/         # Zustand 状态管理
│   │   ├── services/       # API 服务
│   │   └── themes/         # 主题配置
│   └── package.json
├── admin-frontend/         # 管理前端 (React + Vite)
│   ├── src/
│   │   ├── components/     # 管理组件
│   │   ├── pages/          # 管理页面
│   │   └── layouts/        # 布局组件
│   └── package.json
├── doc/                    # 项目文档
│   ├── PROJECT_STATUS.md   # 项目状态
│   └── USAGE.md           # 使用说明
├── xiaoye.sh              # 项目管理脚本
├── start.sh               # 快速启动脚本
├── stop.sh                # 快速停止脚本
└── README.md              # 项目说明
```

## 🔧 开发指南

### 开发环境配置

1. **代码编辑器**: 推荐使用 VS Code
2. **必要插件**:
   - TypeScript and JavaScript Language Features
   - ES7+ React/Redux/React-Native snippets
   - Prettier - Code formatter
   - ESLint

### 开发命令

```bash
# 后端开发
cd backend
npm run start:dev      # 开发模式启动
npm run build          # 构建生产版本
npm run test           # 运行测试

# 前端开发
cd frontend
npm run dev            # 开发模式启动
npm run build          # 构建生产版本
npm run preview        # 预览生产版本

# 管理前端开发
cd admin-frontend
npm run dev            # 开发模式启动
npm run build          # 构建生产版本
```

### API 开发

后端 API 遵循 RESTful 设计原则：

- `GET /api/bookmarks` - 获取书签列表
- `POST /api/bookmarks` - 创建书签
- `PUT /api/bookmarks/:id` - 更新书签
- `DELETE /api/bookmarks/:id` - 删除书签

详细 API 文档请参考 `doc/` 目录下的相关文档。

## 🚀 部署说明

### 生产环境部署

1. **构建项目**:

```bash
# 构建后端
cd backend && npm run build

# 构建前端
cd ../frontend && npm run build

# 构建管理前端
cd ../admin-frontend && npm run build
```

2. **环境变量配置**:

```env
NODE_ENV=production
DB_HOST=your-production-db-host
DB_PASSWORD=your-production-password
JWT_SECRET=your-production-jwt-secret
```

3. **使用 PM2 部署**:

```bash
npm install -g pm2
pm2 start ecosystem.config.js
```

### Docker 部署

```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d
```

## 🛠️ 故障排除

### 常见问题

1. **端口被占用**:

```bash
# 查看端口占用
lsof -i :3000
lsof -i :5173
lsof -i :5174

# 停止所有服务
./xiaoye.sh stop
```

2. **数据库连接失败**:

- 检查 PostgreSQL 服务是否启动
- 验证 `.env` 文件中的数据库配置
- 确认数据库用户权限

3. **依赖安装失败**:

```bash
# 清除缓存重新安装
rm -rf node_modules package-lock.json
npm install
```

4. **前端编译错误**:

```bash
# 清除 Vite 缓存
rm -rf node_modules/.vite
npm run dev
```

### 获取帮助

- 查看项目文档: `doc/` 目录
- 查看使用说明: `doc/USAGE.md`
- 查看项目状态: `doc/PROJECT_STATUS.md`

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 贡献指南

欢迎贡献代码！请遵循以下步骤：

1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📞 联系方式

- 项目主页: [GitHub Repository]
- 问题反馈: [GitHub Issues]
- 邮箱: [<EMAIL>]

---

**小野在线导航** - 让网址管理更简单 🚀

角色 邮箱 密码 说明
管理员 <EMAIL> admin123456 完整管理权限
测试用户 <EMAIL> user123456 普通用户权限
