#!/bin/bash

# 小野在线导航项目安装脚本
# 自动安装所有依赖并进行基础配置

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 未安装，请先安装 $1"
        return 1
    fi
    return 0
}

# 检查系统要求
check_requirements() {
    log_info "检查系统要求..."
    
    # 检查 Node.js
    if check_command "node"; then
        NODE_VERSION=$(node --version | cut -d'v' -f2)
        log_success "Node.js 版本: $NODE_VERSION"
    else
        log_error "请安装 Node.js >= 18.0.0"
        exit 1
    fi
    
    # 检查 npm
    if check_command "npm"; then
        NPM_VERSION=$(npm --version)
        log_success "npm 版本: $NPM_VERSION"
    else
        log_error "请安装 npm >= 9.0.0"
        exit 1
    fi
    
    # 检查 PostgreSQL
    if check_command "psql"; then
        PSQL_VERSION=$(psql --version | awk '{print $3}')
        log_success "PostgreSQL 版本: $PSQL_VERSION"
    else
        log_warning "PostgreSQL 未检测到，请确保已安装并配置"
    fi
    
    # 检查 Git
    if check_command "git"; then
        GIT_VERSION=$(git --version | awk '{print $3}')
        log_success "Git 版本: $GIT_VERSION"
    else
        log_warning "Git 未检测到，建议安装用于版本控制"
    fi
}

# 安装依赖
install_dependencies() {
    log_info "开始安装项目依赖..."
    
    # 安装后端依赖
    log_info "安装后端依赖..."
    cd backend
    if npm install; then
        log_success "后端依赖安装完成"
    else
        log_error "后端依赖安装失败"
        exit 1
    fi
    cd ..
    
    # 安装用户前端依赖
    log_info "安装用户前端依赖..."
    cd frontend
    if npm install; then
        log_success "用户前端依赖安装完成"
    else
        log_error "用户前端依赖安装失败"
        exit 1
    fi
    cd ..
    
    # 安装管理前端依赖
    log_info "安装管理前端依赖..."
    cd admin-frontend
    if npm install; then
        log_success "管理前端依赖安装完成"
    else
        log_error "管理前端依赖安装失败"
        exit 1
    fi
    cd ..
}

# 配置环境
setup_environment() {
    log_info "配置开发环境..."
    
    # 复制环境变量文件
    if [ ! -f "backend/.env" ]; then
        if [ -f "backend/.env.example" ]; then
            cp backend/.env.example backend/.env
            log_success "已创建 backend/.env 文件"
            log_warning "请编辑 backend/.env 文件配置数据库连接信息"
        else
            log_warning "backend/.env.example 文件不存在"
        fi
    else
        log_info "backend/.env 文件已存在"
    fi
    
    # 设置脚本执行权限
    chmod +x xiaoye.sh start.sh stop.sh install.sh
    log_success "已设置脚本执行权限"
}

# 显示安装完成信息
show_completion_info() {
    echo ""
    echo -e "${GREEN}🎉 安装完成！${NC}"
    echo ""
    echo -e "${BLUE}📋 下一步操作：${NC}"
    echo "1. 配置数据库连接信息："
    echo "   编辑 backend/.env 文件"
    echo ""
    echo "2. 启动项目："
    echo "   ./start.sh"
    echo ""
    echo "3. 访问应用："
    echo "   用户前端: http://localhost:5173"
    echo "   管理前端: http://localhost:5174"
    echo "   后端 API: http://localhost:3000/api"
    echo ""
    echo -e "${YELLOW}💡 提示：${NC}"
    echo "- 使用 ./xiaoye.sh status 查看服务状态"
    echo "- 使用 ./xiaoye.sh stop 停止所有服务"
    echo "- 查看 README.md 了解更多信息"
    echo ""
}

# 主函数
main() {
    echo -e "${BLUE}🚀 小野在线导航 - 项目安装脚本${NC}"
    echo ""
    
    check_requirements
    echo ""
    
    install_dependencies
    echo ""
    
    setup_environment
    echo ""
    
    show_completion_info
}

# 运行主函数
main
