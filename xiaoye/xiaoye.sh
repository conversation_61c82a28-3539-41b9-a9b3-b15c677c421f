#!/bin/bash

# 小野在线导航项目管理脚本
# 用于启动、停止、重启整个项目

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目路径
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKEND_DIR="$PROJECT_DIR/backend"
FRONTEND_DIR="$PROJECT_DIR/frontend"
ADMIN_FRONTEND_DIR="$PROJECT_DIR/admin-frontend"

# PID文件路径
PID_DIR="$PROJECT_DIR/.pids"
BACKEND_PID="$PID_DIR/backend.pid"
FRONTEND_PID="$PID_DIR/frontend.pid"
ADMIN_PID="$PID_DIR/admin.pid"

# 创建PID目录
mkdir -p "$PID_DIR"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查进程是否运行
is_running() {
    local pid_file=$1
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0
        else
            rm -f "$pid_file"
            return 1
        fi
    fi
    return 1
}

# 启动后端
start_backend() {
    log_info "启动后端服务..."
    if is_running "$BACKEND_PID"; then
        log_warning "后端服务已在运行中"
        return 0
    fi
    
    cd "$BACKEND_DIR"
    if [ ! -d "node_modules" ]; then
        log_info "安装后端依赖..."
        npm install
    fi
    
    nohup npm run start:dev > "$PROJECT_DIR/logs/backend.log" 2>&1 &
    echo $! > "$BACKEND_PID"
    log_success "后端服务已启动 (PID: $(cat $BACKEND_PID))"
}

# 启动用户前端
start_frontend() {
    log_info "启动用户前端..."
    if is_running "$FRONTEND_PID"; then
        log_warning "用户前端已在运行中"
        return 0
    fi
    
    cd "$FRONTEND_DIR"
    if [ ! -d "node_modules" ]; then
        log_info "安装前端依赖..."
        npm install
    fi
    
    nohup npm run dev > "$PROJECT_DIR/logs/frontend.log" 2>&1 &
    echo $! > "$FRONTEND_PID"
    log_success "用户前端已启动 (PID: $(cat $FRONTEND_PID))"
}

# 启动管理前端
start_admin() {
    log_info "启动管理前端..."
    if is_running "$ADMIN_PID"; then
        log_warning "管理前端已在运行中"
        return 0
    fi
    
    cd "$ADMIN_FRONTEND_DIR"
    if [ ! -d "node_modules" ]; then
        log_info "安装管理前端依赖..."
        npm install
    fi
    
    nohup npm run dev > "$PROJECT_DIR/logs/admin.log" 2>&1 &
    echo $! > "$ADMIN_PID"
    log_success "管理前端已启动 (PID: $(cat $ADMIN_PID))"
}

# 停止服务
stop_service() {
    local service_name=$1
    local pid_file=$2
    
    if is_running "$pid_file"; then
        local pid=$(cat "$pid_file")
        log_info "停止 $service_name (PID: $pid)..."
        kill "$pid"
        sleep 2
        
        if ps -p "$pid" > /dev/null 2>&1; then
            log_warning "强制停止 $service_name..."
            kill -9 "$pid"
        fi
        
        rm -f "$pid_file"
        log_success "$service_name 已停止"
    else
        log_warning "$service_name 未在运行"
    fi
}

# 检查服务状态
check_status() {
    echo -e "\n${BLUE}=== 小野在线导航 - 服务状态 ===${NC}"
    
    if is_running "$BACKEND_PID"; then
        echo -e "${GREEN}✓${NC} 后端服务: 运行中 (PID: $(cat $BACKEND_PID)) - http://localhost:3000/api"
    else
        echo -e "${RED}✗${NC} 后端服务: 未运行"
    fi
    
    if is_running "$FRONTEND_PID"; then
        echo -e "${GREEN}✓${NC} 用户前端: 运行中 (PID: $(cat $FRONTEND_PID)) - http://localhost:5173"
    else
        echo -e "${RED}✗${NC} 用户前端: 未运行"
    fi
    
    if is_running "$ADMIN_PID"; then
        echo -e "${GREEN}✓${NC} 管理前端: 运行中 (PID: $(cat $ADMIN_PID)) - http://localhost:5174"
    else
        echo -e "${RED}✗${NC} 管理前端: 未运行"
    fi
    echo ""
}

# 显示日志
show_logs() {
    local service=$1
    case $service in
        "backend")
            tail -f "$PROJECT_DIR/logs/backend.log"
            ;;
        "frontend")
            tail -f "$PROJECT_DIR/logs/frontend.log"
            ;;
        "admin")
            tail -f "$PROJECT_DIR/logs/admin.log"
            ;;
        *)
            echo "用法: $0 logs [backend|frontend|admin]"
            ;;
    esac
}

# 创建日志目录
mkdir -p "$PROJECT_DIR/logs"

# 主函数
case "$1" in
    "start")
        log_info "启动小野在线导航项目..."
        start_backend
        sleep 3
        start_frontend
        sleep 2
        start_admin
        sleep 2
        check_status
        ;;
    "stop")
        log_info "停止小野在线导航项目..."
        stop_service "管理前端" "$ADMIN_PID"
        stop_service "用户前端" "$FRONTEND_PID"
        stop_service "后端服务" "$BACKEND_PID"
        check_status
        ;;
    "restart")
        log_info "重启小野在线导航项目..."
        $0 stop
        sleep 3
        $0 start
        ;;
    "status")
        check_status
        ;;
    "logs")
        show_logs "$2"
        ;;
    "backend")
        case "$2" in
            "start") start_backend ;;
            "stop") stop_service "后端服务" "$BACKEND_PID" ;;
            "restart") 
                stop_service "后端服务" "$BACKEND_PID"
                sleep 2
                start_backend
                ;;
            *) echo "用法: $0 backend [start|stop|restart]" ;;
        esac
        ;;
    "frontend")
        case "$2" in
            "start") start_frontend ;;
            "stop") stop_service "用户前端" "$FRONTEND_PID" ;;
            "restart")
                stop_service "用户前端" "$FRONTEND_PID"
                sleep 2
                start_frontend
                ;;
            *) echo "用法: $0 frontend [start|stop|restart]" ;;
        esac
        ;;
    "admin")
        case "$2" in
            "start") start_admin ;;
            "stop") stop_service "管理前端" "$ADMIN_PID" ;;
            "restart")
                stop_service "管理前端" "$ADMIN_PID"
                sleep 2
                start_admin
                ;;
            *) echo "用法: $0 admin [start|stop|restart]" ;;
        esac
        ;;
    *)
        echo -e "${BLUE}小野在线导航项目管理脚本${NC}"
        echo ""
        echo "用法: $0 [命令] [选项]"
        echo ""
        echo "主要命令:"
        echo "  start     启动所有服务"
        echo "  stop      停止所有服务"
        echo "  restart   重启所有服务"
        echo "  status    查看服务状态"
        echo ""
        echo "单独服务管理:"
        echo "  backend [start|stop|restart]    管理后端服务"
        echo "  frontend [start|stop|restart]   管理用户前端"
        echo "  admin [start|stop|restart]      管理管理前端"
        echo ""
        echo "日志查看:"
        echo "  logs [backend|frontend|admin]   查看服务日志"
        echo ""
        echo "示例:"
        echo "  $0 start                启动所有服务"
        echo "  $0 backend restart      重启后端服务"
        echo "  $0 logs backend         查看后端日志"
        ;;
esac
