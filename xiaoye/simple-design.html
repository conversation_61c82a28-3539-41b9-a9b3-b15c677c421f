<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简约设计 - 小野在线导航</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 8px;
            padding: 40px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 800px;
            width: 100%;
        }
        
        h1 {
            font-size: 2.5rem;
            font-weight: 300;
            color: #333;
            margin-bottom: 20px;
        }
        
        .subtitle {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 40px;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin: 40px 0;
            text-align: left;
        }
        
        .feature {
            padding: 20px;
            border-left: 4px solid #1976d2;
            background: #f9f9f9;
        }
        
        .feature h3 {
            font-size: 1.2rem;
            font-weight: 600;
            color: #1976d2;
            margin-bottom: 10px;
        }
        
        .feature p {
            color: #666;
            line-height: 1.6;
        }
        
        .comparison {
            margin: 40px 0;
            padding: 30px;
            background: #f0f8ff;
            border-radius: 8px;
            border: 1px solid #e3f2fd;
        }
        
        .comparison h3 {
            color: #1976d2;
            margin-bottom: 20px;
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            text-align: left;
        }
        
        .before, .after {
            padding: 15px;
            border-radius: 4px;
        }
        
        .before {
            background: #ffebee;
            border-left: 4px solid #f44336;
        }
        
        .after {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
        }
        
        .before h4 {
            color: #d32f2f;
            margin-bottom: 10px;
        }
        
        .after h4 {
            color: #2e7d32;
            margin-bottom: 10px;
        }
        
        .links {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
            margin-top: 40px;
        }
        
        .link {
            display: inline-block;
            padding: 12px 24px;
            background: #1976d2;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-weight: 500;
            transition: background 0.3s ease;
        }
        
        .link:hover {
            background: #1565c0;
        }
        
        .admin-link {
            background: #666;
        }
        
        .admin-link:hover {
            background: #555;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 30px 20px;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .comparison-grid {
                grid-template-columns: 1fr;
            }
            
            .links {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>简约设计重构完成</h1>
        <p class="subtitle">小野在线导航 - 全新简约界面设计</p>
        
        <div class="features">
            <div class="feature">
                <h3>🎯 完美居中</h3>
                <p>使用 Flexbox 实现真正的页面居中，所有内容都在屏幕中央完美对齐。</p>
            </div>
            
            <div class="feature">
                <h3>💻 电脑端优化</h3>
                <p>专为电脑端设计的合适尺寸，最大宽度限制确保在大屏幕上的最佳显示效果。</p>
            </div>
            
            <div class="feature">
                <h3>🎨 简约美学</h3>
                <p>去除所有复杂的装饰，采用简洁的线条、合适的留白和清晰的层次结构。</p>
            </div>
            
            <div class="feature">
                <h3>⚡ 高效交互</h3>
                <p>直观的操作流程，最少的点击次数，专注于核心功能的实现。</p>
            </div>
        </div>
        
        <div class="comparison">
            <h3>设计对比</h3>
            <div class="comparison-grid">
                <div class="before">
                    <h4>❌ 之前的问题</h4>
                    <ul style="margin: 0; padding-left: 20px; color: #666;">
                        <li>复杂的渐变和毛玻璃效果</li>
                        <li>过多的动画和装饰元素</li>
                        <li>不够居中的布局</li>
                        <li>过于花哨的视觉效果</li>
                        <li>分散注意力的设计元素</li>
                    </ul>
                </div>
                <div class="after">
                    <h4>✅ 现在的优势</h4>
                    <ul style="margin: 0; padding-left: 20px; color: #666;">
                        <li>简洁的白色背景和清晰边框</li>
                        <li>专注于内容的极简设计</li>
                        <li>完美的页面居中布局</li>
                        <li>适合电脑端的合理尺寸</li>
                        <li>直观高效的用户体验</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div style="margin: 30px 0; padding: 20px; background: #fff3e0; border-radius: 8px; border-left: 4px solid #ff9800;">
            <h3 style="color: #f57c00; margin-bottom: 15px;">🎯 设计原则</h3>
            <p style="color: #666; text-align: left; line-height: 1.6;">
                <strong>简约至上</strong> - 去除一切不必要的装饰元素<br>
                <strong>功能优先</strong> - 专注于核心功能的实现<br>
                <strong>完美居中</strong> - 所有内容在页面中完美对齐<br>
                <strong>电脑端适配</strong> - 专为桌面端优化的尺寸和布局<br>
                <strong>清晰层次</strong> - 明确的信息架构和视觉层次
            </p>
        </div>
        
        <div class="links">
            <a href="http://localhost:5173" class="link" target="_blank">
                📱 用户前端 (简约设计)
            </a>
            <a href="http://localhost:5174" class="link admin-link" target="_blank">
                🔧 管理后台 (简约设计)
            </a>
        </div>
    </div>
</body>
</html>
