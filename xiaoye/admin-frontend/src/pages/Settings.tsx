import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  TextField,
  Switch,
  FormControlLabel,
  Button,
  Grid,
  Divider,
  Alert,
  Snackbar,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
} from '@mui/material';
import {
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Edit as EditIcon,
} from '@mui/icons-material';
import axios from 'axios';

interface SystemSetting {
  id: string;
  key: string;
  value: string;
  type: 'boolean' | 'string' | 'number' | 'json';
  description: string;
  isPublic: boolean;
  isEditable: boolean;
}

export const Settings: React.FC = () => {
  const [settings, setSettings] = useState<SystemSetting[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  
  // 分组设置
  const [siteSettings, setSiteSettings] = useState({
    'site.name': '小野导航',
    'site.description': '个人书签管理工具',
    'ui.defaultTheme': 'light',
    'ui.defaultLanguage': 'zh',
  });

  const [authSettings, setAuthSettings] = useState({
    'auth.allowRegistration': true,
    'auth.requireEmailVerification': false,
    'auth.maxLoginAttempts': 5,
  });

  const [bookmarkSettings, setBookmarkSettings] = useState({
    'bookmarks.maxPerUser': 1000,
    'bookmarks.defaultCategories': ['新闻资讯', '视频网站', '游戏直播', '社交媒体', '购物网站'],
  });

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const response = await axios.get('/api/system-settings', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
        },
      });

      if (response.data) {
        setSettings(response.data);
        
        // 解析设置到对应的状态
        const settingsMap: Record<string, any> = {};
        response.data.forEach((setting: SystemSetting) => {
          settingsMap[setting.key] = parseSettingValue(setting);
        });

        // 更新各个设置组
        setSiteSettings(prev => ({ ...prev, ...settingsMap }));
        setAuthSettings(prev => ({ ...prev, ...settingsMap }));
        setBookmarkSettings(prev => ({ ...prev, ...settingsMap }));
      }
    } catch (error) {
      console.error('加载设置失败:', error);
      setMessage({ type: 'error', text: '加载设置失败' });
    } finally {
      setLoading(false);
    }
  };

  const parseSettingValue = (setting: SystemSetting) => {
    switch (setting.type) {
      case 'boolean':
        return setting.value === 'true';
      case 'number':
        return parseFloat(setting.value);
      case 'json':
        try {
          return JSON.parse(setting.value);
        } catch {
          return setting.value;
        }
      default:
        return setting.value;
    }
  };

  const saveSettings = async () => {
    setSaving(true);
    try {
      const allSettings = {
        ...siteSettings,
        ...authSettings,
        ...bookmarkSettings,
      };

      await axios.put('/api/system-settings/batch/update', allSettings, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`,
          'Content-Type': 'application/json',
        },
      });

      setMessage({ type: 'success', text: '设置保存成功' });
      loadSettings(); // 重新加载设置
    } catch (error) {
      console.error('保存设置失败:', error);
      setMessage({ type: 'error', text: '保存设置失败' });
    } finally {
      setSaving(false);
    }
  };

  const handleSiteSettingChange = (key: string, value: any) => {
    setSiteSettings(prev => ({ ...prev, [key]: value }));
  };

  const handleAuthSettingChange = (key: string, value: any) => {
    setAuthSettings(prev => ({ ...prev, [key]: value }));
  };

  const handleBookmarkSettingChange = (key: string, value: any) => {
    setBookmarkSettings(prev => ({ ...prev, [key]: value }));
  };

  const getSettingTypeChip = (type: string) => {
    const colors: Record<string, any> = {
      boolean: 'primary',
      string: 'default',
      number: 'secondary',
      json: 'warning',
    };
    return <Chip label={type} color={colors[type]} size="small" />;
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 300 }}>
          系统设置
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <IconButton onClick={loadSettings}>
            <RefreshIcon />
          </IconButton>
          <Button
            variant="contained"
            startIcon={<SaveIcon />}
            onClick={saveSettings}
            disabled={saving}
          >
            {saving ? '保存中...' : '保存设置'}
          </Button>
        </Box>
      </Box>

      <Grid container spacing={3}>
        {/* 网站设置 */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                网站设置
              </Typography>
              <TextField
                fullWidth
                label="网站名称"
                value={siteSettings['site.name']}
                onChange={(e) => handleSiteSettingChange('site.name', e.target.value)}
                sx={{ mb: 2 }}
              />
              <TextField
                fullWidth
                label="网站描述"
                value={siteSettings['site.description']}
                onChange={(e) => handleSiteSettingChange('site.description', e.target.value)}
                sx={{ mb: 2 }}
              />
              <TextField
                fullWidth
                label="默认主题"
                value={siteSettings['ui.defaultTheme']}
                onChange={(e) => handleSiteSettingChange('ui.defaultTheme', e.target.value)}
                sx={{ mb: 2 }}
              />
              <TextField
                fullWidth
                label="默认语言"
                value={siteSettings['ui.defaultLanguage']}
                onChange={(e) => handleSiteSettingChange('ui.defaultLanguage', e.target.value)}
              />
            </CardContent>
          </Card>
        </Grid>

        {/* 认证设置 */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                认证设置
              </Typography>
              <FormControlLabel
                control={
                  <Switch
                    checked={authSettings['auth.allowRegistration']}
                    onChange={(e) => handleAuthSettingChange('auth.allowRegistration', e.target.checked)}
                  />
                }
                label="允许用户注册"
                sx={{ mb: 2, display: 'block' }}
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={authSettings['auth.requireEmailVerification']}
                    onChange={(e) => handleAuthSettingChange('auth.requireEmailVerification', e.target.checked)}
                  />
                }
                label="需要邮箱验证"
                sx={{ mb: 2, display: 'block' }}
              />
              <TextField
                fullWidth
                label="最大登录尝试次数"
                type="number"
                value={authSettings['auth.maxLoginAttempts']}
                onChange={(e) => handleAuthSettingChange('auth.maxLoginAttempts', parseInt(e.target.value))}
              />
            </CardContent>
          </Card>
        </Grid>

        {/* 书签设置 */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                书签设置
              </Typography>
              <TextField
                fullWidth
                label="每个用户最大书签数量"
                type="number"
                value={bookmarkSettings['bookmarks.maxPerUser']}
                onChange={(e) => handleBookmarkSettingChange('bookmarks.maxPerUser', parseInt(e.target.value))}
                sx={{ mb: 2 }}
              />
              <TextField
                fullWidth
                label="默认书签分类 (用逗号分隔)"
                value={Array.isArray(bookmarkSettings['bookmarks.defaultCategories']) 
                  ? bookmarkSettings['bookmarks.defaultCategories'].join(', ')
                  : bookmarkSettings['bookmarks.defaultCategories']
                }
                onChange={(e) => handleBookmarkSettingChange('bookmarks.defaultCategories', e.target.value.split(', '))}
                helperText="例如: 新闻资讯, 视频网站, 游戏直播"
              />
            </CardContent>
          </Card>
        </Grid>

        {/* 所有设置列表 */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                所有系统设置
              </Typography>
              <TableContainer component={Paper} variant="outlined">
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>键</TableCell>
                      <TableCell>值</TableCell>
                      <TableCell>类型</TableCell>
                      <TableCell>描述</TableCell>
                      <TableCell>公开</TableCell>
                      <TableCell>可编辑</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {settings.map((setting) => (
                      <TableRow key={setting.id}>
                        <TableCell sx={{ fontFamily: 'monospace', fontSize: '0.875rem' }}>
                          {setting.key}
                        </TableCell>
                        <TableCell sx={{ maxWidth: 200, overflow: 'hidden', textOverflow: 'ellipsis' }}>
                          {setting.value}
                        </TableCell>
                        <TableCell>
                          {getSettingTypeChip(setting.type)}
                        </TableCell>
                        <TableCell>{setting.description}</TableCell>
                        <TableCell>
                          <Chip
                            label={setting.isPublic ? '是' : '否'}
                            color={setting.isPublic ? 'success' : 'default'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={setting.isEditable ? '是' : '否'}
                            color={setting.isEditable ? 'primary' : 'default'}
                            size="small"
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* 消息提示 */}
      <Snackbar
        open={!!message}
        autoHideDuration={6000}
        onClose={() => setMessage(null)}
      >
        <Alert
          onClose={() => setMessage(null)}
          severity={message?.type}
          sx={{ width: '100%' }}
        >
          {message?.text}
        </Alert>
      </Snackbar>
    </Box>
  );
};
