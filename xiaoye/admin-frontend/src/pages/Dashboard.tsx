import React, { useState, useEffect } from 'react';
import {
  Box,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  AppBar,
  Toolbar,
  IconButton,
} from '@mui/material';
import { Logout, People, Bookmark, Category } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';

interface AdminStats {
  totalUsers: number;
  totalBookmarks: number;
  totalCategories: number;
  activeUsers: number;
}

export const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<AdminStats>({
    totalUsers: 0,
    totalBookmarks: 0,
    totalCategories: 0,
    activeUsers: 0,
  });
  const navigate = useNavigate();

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      // 模拟数据，实际应该从API获取
      setStats({
        totalUsers: 12,
        totalBookmarks: 156,
        totalCategories: 8,
        activeUsers: 9,
      });
    } catch (error) {
      console.error('Failed to load stats:', error);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('admin_token');
    localStorage.removeItem('user');
    navigate('/login');
  };

  const navigateToUsers = () => {
    navigate('/users');
  };

  return (
    <Box
      sx={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        bgcolor: '#f5f5f5',
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      {/* 顶部导航 */}
      <AppBar position="static" elevation={0} sx={{ bgcolor: '#fff', color: '#333' }}>
        <Toolbar>
          <Typography variant="h6" sx={{ flexGrow: 1, fontWeight: 300 }}>
            小野导航 - 管理后台
          </Typography>
          <IconButton onClick={handleLogout} sx={{ color: '#666' }}>
            <Logout />
          </IconButton>
        </Toolbar>
      </AppBar>

      {/* 主内容 - 真正居中 */}
      <Box
        sx={{
          flex: 1,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          p: 4,
        }}
      >
        <Box sx={{ width: 800 }}>
          <Box sx={{ textAlign: 'center', mb: 6 }}>
            <Typography
              variant="h4"
              sx={{
                mb: 2,
                fontWeight: 300,
                color: '#333',
              }}
            >
              📊 系统概览
            </Typography>
            <Typography variant="body2" sx={{ color: '#666' }}>
              小野导航管理控制台 · 实时数据统计
            </Typography>
            <Typography variant="caption" sx={{ color: '#999', display: 'block', mt: 1 }}>
              最后更新: {new Date().toLocaleString()}
            </Typography>
          </Box>

          {/* 统计卡片 */}
          <Grid container spacing={4} sx={{ mb: 6 }}>
            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ textAlign: 'center', boxShadow: '0 2px 10px rgba(0,0,0,0.1)' }}>
                <CardContent>
                  <People sx={{ fontSize: 40, color: '#1976d2', mb: 2 }} />
                  <Typography variant="h4" sx={{ fontWeight: 300, mb: 1 }}>
                    {stats.totalUsers}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    总用户数
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ textAlign: 'center', boxShadow: '0 2px 10px rgba(0,0,0,0.1)' }}>
                <CardContent>
                  <Bookmark sx={{ fontSize: 40, color: '#4caf50', mb: 2 }} />
                  <Typography variant="h4" sx={{ fontWeight: 300, mb: 1 }}>
                    {stats.totalBookmarks}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    总书签数
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ textAlign: 'center', boxShadow: '0 2px 10px rgba(0,0,0,0.1)' }}>
                <CardContent>
                  <Category sx={{ fontSize: 40, color: '#ff9800', mb: 2 }} />
                  <Typography variant="h4" sx={{ fontWeight: 300, mb: 1 }}>
                    {stats.totalCategories}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    总分类数
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ textAlign: 'center', boxShadow: '0 2px 10px rgba(0,0,0,0.1)' }}>
                <CardContent>
                  <People sx={{ fontSize: 40, color: '#9c27b0', mb: 2 }} />
                  <Typography variant="h4" sx={{ fontWeight: 300, mb: 1 }}>
                    {stats.activeUsers}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    活跃用户
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* 系统状态和操作 */}
          <Box sx={{ textAlign: 'center', pt: 4, borderTop: '1px solid #e0e0e0' }}>
            <Typography variant="h6" sx={{ mb: 3, fontWeight: 400, color: '#333' }}>
              🛠️ 管理操作
            </Typography>
            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', mb: 3 }}>
              <Button
                variant="contained"
                size="large"
                onClick={navigateToUsers}
                sx={{
                  px: 4,
                  py: 1.5,
                  bgcolor: '#1976d2',
                  '&:hover': { bgcolor: '#1565c0' },
                }}
              >
                👥 管理用户
              </Button>
              <Button
                variant="outlined"
                size="large"
                sx={{
                  px: 4,
                  py: 1.5,
                  borderColor: '#1976d2',
                  color: '#1976d2',
                  '&:hover': {
                    borderColor: '#1565c0',
                    bgcolor: 'rgba(25, 118, 210, 0.05)'
                  },
                }}
              >
                📋 系统日志
              </Button>
            </Box>
            <Typography variant="caption" sx={{ color: '#999' }}>
              系统运行正常 · 服务状态良好
            </Typography>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};
