import React, { useEffect, useState } from 'react';
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  Paper,
} from '@mui/material';
import {
  People,
  Bookmark,
  Category,
  TrendingUp,
} from '@mui/icons-material';
import { AdminStats } from '../types';

const StatCard: React.FC<{
  title: string;
  value: number;
  icon: React.ReactNode;
  color: string;
}> = ({ title, value, icon, color }) => (
  <Card>
    <CardContent>
      <Box display="flex" alignItems="center" justifyContent="space-between">
        <Box>
          <Typography color="textSecondary" gutterBottom variant="body2">
            {title}
          </Typography>
          <Typography variant="h4" component="h2">
            {value.toLocaleString()}
          </Typography>
        </Box>
        <Box
          sx={{
            backgroundColor: color,
            borderRadius: '50%',
            p: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          {icon}
        </Box>
      </Box>
    </CardContent>
  </Card>
);

export const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<AdminStats>({
    totalUsers: 0,
    totalBookmarks: 0,
    totalCategories: 0,
    activeUsers: 0,
  });

  useEffect(() => {
    // TODO: 从API获取统计数据
    // 暂时使用模拟数据
    setStats({
      totalUsers: 156,
      totalBookmarks: 1234,
      totalCategories: 89,
      activeUsers: 142,
    });
  }, []);

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        仪表板
      </Typography>
      
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="总用户数"
            value={stats.totalUsers}
            icon={<People sx={{ color: 'white' }} />}
            color="#1976d2"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="活跃用户"
            value={stats.activeUsers}
            icon={<TrendingUp sx={{ color: 'white' }} />}
            color="#2e7d32"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="总书签数"
            value={stats.totalBookmarks}
            icon={<Bookmark sx={{ color: 'white' }} />}
            color="#ed6c02"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="总分类数"
            value={stats.totalCategories}
            icon={<Category sx={{ color: 'white' }} />}
            color="#9c27b0"
          />
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              最近活动
            </Typography>
            <Typography variant="body2" color="text.secondary">
              用户活动统计图表将在此显示...
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              系统状态
            </Typography>
            <Typography variant="body2" color="text.secondary">
              系统运行状态良好
            </Typography>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};
