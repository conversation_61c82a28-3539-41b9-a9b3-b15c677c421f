import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Button,
  AppBar,
  Toolbar,
  IconButton,
} from '@mui/material';
import { Logout, People, Bookmark, Category } from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';

interface AdminStats {
  totalUsers: number;
  totalBookmarks: number;
  totalCategories: number;
  activeUsers: number;
}

export const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<AdminStats>({
    totalUsers: 0,
    totalBookmarks: 0,
    totalCategories: 0,
    activeUsers: 0,
  });
  const navigate = useNavigate();

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      // 模拟数据，实际应该从API获取
      setStats({
        totalUsers: 12,
        totalBookmarks: 156,
        totalCategories: 8,
        activeUsers: 9,
      });
    } catch (error) {
      console.error('Failed to load stats:', error);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('admin_token');
    localStorage.removeItem('user');
    navigate('/login');
  };

  const navigateToUsers = () => {
    navigate('/users');
  };

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: '#f5f5f5' }}>
      {/* 顶部导航 */}
      <AppBar position="static" elevation={0} sx={{ bgcolor: '#fff', color: '#333' }}>
        <Toolbar>
          <Typography variant="h6" sx={{ flexGrow: 1, fontWeight: 300 }}>
            小野导航 - 管理后台
          </Typography>
          <IconButton onClick={handleLogout} sx={{ color: '#666' }}>
            <Logout />
          </IconButton>
        </Toolbar>
      </AppBar>

      {/* 主内容 */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: 'calc(100vh - 64px)',
          p: 4,
        }}
      >
        <Box sx={{ width: '100%', maxWidth: 800 }}>
          <Typography
            variant="h4"
            sx={{
              textAlign: 'center',
              mb: 6,
              fontWeight: 300,
              color: '#333',
            }}
          >
            系统概览
          </Typography>

          {/* 统计卡片 */}
          <Grid container spacing={4} sx={{ mb: 6 }}>
            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ textAlign: 'center', boxShadow: '0 2px 10px rgba(0,0,0,0.1)' }}>
                <CardContent>
                  <People sx={{ fontSize: 40, color: '#1976d2', mb: 2 }} />
                  <Typography variant="h4" sx={{ fontWeight: 300, mb: 1 }}>
                    {stats.totalUsers}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    总用户数
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ textAlign: 'center', boxShadow: '0 2px 10px rgba(0,0,0,0.1)' }}>
                <CardContent>
                  <Bookmark sx={{ fontSize: 40, color: '#4caf50', mb: 2 }} />
                  <Typography variant="h4" sx={{ fontWeight: 300, mb: 1 }}>
                    {stats.totalBookmarks}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    总书签数
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ textAlign: 'center', boxShadow: '0 2px 10px rgba(0,0,0,0.1)' }}>
                <CardContent>
                  <Category sx={{ fontSize: 40, color: '#ff9800', mb: 2 }} />
                  <Typography variant="h4" sx={{ fontWeight: 300, mb: 1 }}>
                    {stats.totalCategories}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    总分类数
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ textAlign: 'center', boxShadow: '0 2px 10px rgba(0,0,0,0.1)' }}>
                <CardContent>
                  <People sx={{ fontSize: 40, color: '#9c27b0', mb: 2 }} />
                  <Typography variant="h4" sx={{ fontWeight: 300, mb: 1 }}>
                    {stats.activeUsers}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    活跃用户
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* 操作按钮 */}
          <Box sx={{ textAlign: 'center' }}>
            <Button
              variant="contained"
              size="large"
              onClick={navigateToUsers}
              sx={{
                px: 4,
                py: 1.5,
                bgcolor: '#1976d2',
                '&:hover': { bgcolor: '#1565c0' },
              }}
            >
              管理用户
            </Button>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};
