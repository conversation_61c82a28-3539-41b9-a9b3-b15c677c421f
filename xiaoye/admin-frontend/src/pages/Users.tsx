import React, { useEffect, useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  IconButton,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  DataGrid,
  GridColDef,
  GridActionsCellItem,
} from '@mui/x-data-grid';
import {
  Edit,
  Delete,
  Block,
  CheckCircle,
} from '@mui/icons-material';
import { User } from '../types';
import { userApi } from '../services/api';

export const Users: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  useEffect(() => {
    loadUsers();
  }, []);

  const loadUsers = async () => {
    try {
      // TODO: 实际API调用
      // const response = await userApi.getUsers();
      // setUsers(response.data);
      
      // 暂时使用模拟数据
      setUsers([
        {
          id: '1',
          email: '<EMAIL>',
          name: '张三',
          role: 'user',
          isActive: true,
          createdAt: '2024-01-15T10:30:00Z',
          updatedAt: '2024-01-15T10:30:00Z',
        },
        {
          id: '2',
          email: '<EMAIL>',
          name: '李四',
          role: 'user',
          isActive: false,
          createdAt: '2024-01-16T14:20:00Z',
          updatedAt: '2024-01-16T14:20:00Z',
        },
        {
          id: '3',
          email: '<EMAIL>',
          name: '管理员',
          role: 'admin',
          isActive: true,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
        },
      ]);
    } catch (error) {
      console.error('Failed to load users:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleToggleStatus = async (user: User) => {
    try {
      // await userApi.toggleUserStatus(user.id);
      setUsers(users.map(u => 
        u.id === user.id ? { ...u, isActive: !u.isActive } : u
      ));
    } catch (error) {
      console.error('Failed to toggle user status:', error);
    }
  };

  const handleDeleteUser = async () => {
    if (!selectedUser) return;
    
    try {
      // await userApi.deleteUser(selectedUser.id);
      setUsers(users.filter(u => u.id !== selectedUser.id));
      setDeleteDialogOpen(false);
      setSelectedUser(null);
    } catch (error) {
      console.error('Failed to delete user:', error);
    }
  };

  const columns: GridColDef[] = [
    { field: 'id', headerName: 'ID', width: 100 },
    { field: 'email', headerName: '邮箱', width: 200 },
    { field: 'name', headerName: '姓名', width: 150 },
    {
      field: 'role',
      headerName: '角色',
      width: 100,
      renderCell: (params) => (
        <Chip
          label={params.value === 'admin' ? '管理员' : '用户'}
          color={params.value === 'admin' ? 'primary' : 'default'}
          size="small"
        />
      ),
    },
    {
      field: 'isActive',
      headerName: '状态',
      width: 100,
      renderCell: (params) => (
        <Chip
          label={params.value ? '活跃' : '禁用'}
          color={params.value ? 'success' : 'error'}
          size="small"
        />
      ),
    },
    {
      field: 'createdAt',
      headerName: '创建时间',
      width: 180,
      valueFormatter: (params) => new Date(params.value).toLocaleString(),
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: '操作',
      width: 150,
      getActions: (params) => [
        <GridActionsCellItem
          icon={<Edit />}
          label="编辑"
          onClick={() => console.log('Edit user:', params.row)}
        />,
        <GridActionsCellItem
          icon={params.row.isActive ? <Block /> : <CheckCircle />}
          label={params.row.isActive ? '禁用' : '启用'}
          onClick={() => handleToggleStatus(params.row)}
        />,
        <GridActionsCellItem
          icon={<Delete />}
          label="删除"
          onClick={() => {
            setSelectedUser(params.row);
            setDeleteDialogOpen(true);
          }}
        />,
      ],
    },
  ];

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        用户管理
      </Typography>
      
      <Paper sx={{ height: 600, width: '100%' }}>
        <DataGrid
          rows={users}
          columns={columns}
          loading={loading}
          pageSizeOptions={[10, 25, 50]}
          disableRowSelectionOnClick
        />
      </Paper>

      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>确认删除</DialogTitle>
        <DialogContent>
          确定要删除用户 "{selectedUser?.name || selectedUser?.email}" 吗？
          此操作不可撤销。
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>取消</Button>
          <Button onClick={handleDeleteUser} color="error">
            删除
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};
