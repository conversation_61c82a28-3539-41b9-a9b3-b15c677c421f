export interface User {
  id: string;
  email: string;
  name?: string;
  avatar?: string;
  role: 'user' | 'admin';
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Category {
  id: string;
  name: string;
  description?: string;
  icon?: string;
  color?: string;
  sortOrder: number;
  isVisible: boolean;
  isQuickAccess: boolean;
  userId: string;
  user?: User;
  createdAt: string;
  updatedAt: string;
}

export interface Bookmark {
  id: string;
  title: string;
  url: string;
  description?: string;
  icon?: string;
  favicon?: string;
  sortOrder: number;
  isVisible: boolean;
  isQuickAccess: boolean;
  userId: string;
  categoryId?: string;
  user?: User;
  category?: Category;
  createdAt: string;
  updatedAt: string;
}

export interface AdminStats {
  totalUsers: number;
  totalBookmarks: number;
  totalCategories: number;
  activeUsers: number;
}
