import axios from 'axios';

const API_BASE_URL = 'http://localhost:3000/api';

export const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器 - 添加认证token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('admin_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - 处理错误
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('admin_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// 用户管理API
export const userApi = {
  getUsers: () => api.get('/admin/users'),
  getUserById: (id: string) => api.get(`/admin/users/${id}`),
  updateUser: (id: string, data: any) => api.put(`/admin/users/${id}`, data),
  deleteUser: (id: string) => api.delete(`/admin/users/${id}`),
  toggleUserStatus: (id: string) => api.patch(`/admin/users/${id}/toggle-status`),
};

// 书签管理API
export const bookmarkApi = {
  getAllBookmarks: () => api.get('/admin/bookmarks'),
  getBookmarksByUser: (userId: string) => api.get(`/admin/bookmarks/user/${userId}`),
  deleteBookmark: (id: string) => api.delete(`/admin/bookmarks/${id}`),
};

// 分类管理API
export const categoryApi = {
  getAllCategories: () => api.get('/admin/categories'),
  getCategoriesByUser: (userId: string) => api.get(`/admin/categories/user/${userId}`),
  deleteCategory: (id: string) => api.delete(`/admin/categories/${id}`),
};

// 认证API
export const authApi = {
  login: (email: string, password: string) => 
    api.post('/auth/login', { email, password }),
  logout: () => api.post('/auth/logout'),
};
