<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>居中测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            text-align: center;
            max-width: 800px;
            width: 100%;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        h1 {
            font-size: 3rem;
            font-weight: 700;
            background: linear-gradient(45deg, #1976d2 30%, #42a5f5 90%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 20px;
        }

        .subtitle {
            font-size: 1.5rem;
            color: #666;
            margin-bottom: 30px;
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .test-card {
            background: linear-gradient(135deg, rgba(25, 118, 210, 0.1) 0%, rgba(66, 165, 245, 0.05) 100%);
            border-radius: 15px;
            padding: 20px;
            border: 2px solid rgba(25, 118, 210, 0.2);
            transition: all 0.3s ease;
        }

        .test-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(25, 118, 210, 0.3);
        }

        .card-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #1976d2;
            margin-bottom: 10px;
        }

        .card-content {
            color: #666;
            font-size: 0.9rem;
        }

        .status {
            display: inline-block;
            padding: 8px 16px;
            background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%);
            color: white;
            border-radius: 20px;
            font-weight: 600;
            margin: 20px 0;
        }

        .links {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
            margin-top: 30px;
        }

        .link {
            display: inline-block;
            padding: 12px 24px;
            background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(25, 118, 210, 0.3);
        }

        .link:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(25, 118, 210, 0.4);
        }

        .admin-link {
            background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .admin-link:hover {
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }

        @media (max-width: 768px) {
            .container {
                padding: 30px 20px;
            }

            h1 {
                font-size: 2rem;
            }

            .subtitle {
                font-size: 1.2rem;
            }

            .test-grid {
                grid-template-columns: 1fr;
            }

            .links {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 完美居中测试</h1>
        <p class="subtitle">小野在线导航 - 专业级居中布局验证</p>

        <div class="status">✅ 整体页面居中已实现</div>

        <div class="test-grid">
            <div class="test-card">
                <div class="card-title">🎨 视觉居中</div>
                <div class="card-content">所有元素在视觉上完美居中对齐</div>
            </div>

            <div class="test-card">
                <div class="card-title">📱 响应式居中</div>
                <div class="card-content">在所有设备尺寸上都保持居中</div>
            </div>

            <div class="test-card">
                <div class="card-title">🚀 性能优化</div>
                <div class="card-content">使用现代CSS技术实现流畅动画</div>
            </div>

            <div class="test-card">
                <div class="card-title">💎 毛玻璃效果</div>
                <div class="card-content">专业级的视觉层次和深度</div>
            </div>
        </div>

        <div class="links">
            <a href="http://localhost:5173" class="link" target="_blank">
                🏠 用户前端 (完美居中)
            </a>
            <a href="http://localhost:5174" class="link admin-link" target="_blank">
                🔧 管理后台 (专业登录)
            </a>
        </div>

        <div style="margin-top: 30px; padding: 20px; background: rgba(25, 118, 210, 0.1); border-radius: 15px; border-left: 4px solid #1976d2;">
            <h3 style="color: #1976d2; margin-bottom: 10px;">🎯 整体页面居中优化说明</h3>
            <p style="color: #666; text-align: left; line-height: 1.6;">
                • <strong>整体页面居中</strong> - 使用 display: flex + alignItems: center + justifyContent: center<br>
                • <strong>容器完美居中</strong> - maxWidth + 自动边距实现水平居中<br>
                • <strong>垂直居中</strong> - minHeight: 100vh + flex 布局实现垂直居中<br>
                • <strong>响应式居中</strong> - 在所有屏幕尺寸上都保持完美居中<br>
                • <strong>毛玻璃效果</strong> - backdrop-filter 和半透明背景<br>
                • <strong>专业视觉效果</strong> - 渐变背景和深度阴影
            </p>
        </div>
    </div>
</body>
</html>
