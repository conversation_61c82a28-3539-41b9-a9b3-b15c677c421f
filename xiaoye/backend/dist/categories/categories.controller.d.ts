import { CategoriesService } from './categories.service';
import { CreateCategoryDto, UpdateCategoryDto } from './dto/category.dto';
export declare class CategoriesController {
    private readonly categoriesService;
    constructor(categoriesService: CategoriesService);
    create(createCategoryDto: CreateCategoryDto, userId?: string): Promise<import("../types").Category>;
    findAll(userId?: string): Promise<import("../types").Category[]>;
    findQuickAccess(userId?: string): Promise<import("../types").Category[]>;
    findOne(id: string): Promise<import("../types").Category>;
    update(id: string, updateCategoryDto: UpdateCategoryDto, userId?: string): Promise<import("../types").Category>;
    remove(id: string, userId?: string): Promise<void>;
}
