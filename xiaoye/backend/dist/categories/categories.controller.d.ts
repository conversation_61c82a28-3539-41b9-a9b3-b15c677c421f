import { CategoriesService } from './categories.service';
import { CreateCategoryDto, UpdateCategoryDto } from './dto/category.dto';
import { Category } from '../types';
export declare class CategoriesController {
    private readonly categoriesService;
    constructor(categoriesService: CategoriesService);
    create(createCategoryDto: CreateCategoryDto, userId?: string): Promise<Category>;
    findAll(userId?: string): Promise<Category[]>;
    findQuickAccess(userId?: string): Promise<Category[]>;
    findOne(id: string): Promise<Category>;
    update(id: string, updateCategoryDto: UpdateCategoryDto, userId?: string): Promise<Category>;
    remove(id: string, userId?: string): Promise<void>;
}
