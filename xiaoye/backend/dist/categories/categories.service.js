"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CategoriesService = void 0;
const common_1 = require("@nestjs/common");
let CategoriesService = class CategoriesService {
    categories = [
        {
            id: 'cat1',
            name: '搜索引擎',
            description: '各种搜索引擎网站',
            icon: 'search',
            color: '#1976d2',
            sortOrder: 1,
            isVisible: true,
            isQuickAccess: true,
            userId: 'user1',
            createdAt: new Date(),
            updatedAt: new Date(),
        },
        {
            id: 'cat2',
            name: '开发工具',
            description: '编程开发相关工具',
            icon: 'code',
            color: '#2e7d32',
            sortOrder: 2,
            isVisible: true,
            isQuickAccess: false,
            userId: 'user1',
            createdAt: new Date(),
            updatedAt: new Date(),
        },
    ];
    async findAll(userId) {
        if (userId) {
            return this.categories.filter(category => category.userId === userId);
        }
        return this.categories;
    }
    async findOne(id) {
        const category = this.categories.find(c => c.id === id);
        if (!category) {
            throw new common_1.NotFoundException(`Category with ID ${id} not found`);
        }
        return category;
    }
    async create(userId, createCategoryDto) {
        const newCategory = {
            id: Date.now().toString(),
            ...createCategoryDto,
            userId,
            sortOrder: createCategoryDto.sortOrder ?? 0,
            isVisible: createCategoryDto.isVisible ?? true,
            isQuickAccess: createCategoryDto.isQuickAccess ?? false,
            createdAt: new Date(),
            updatedAt: new Date(),
        };
        this.categories.push(newCategory);
        return newCategory;
    }
    async update(id, userId, updateCategoryDto) {
        const categoryIndex = this.categories.findIndex(c => c.id === id && c.userId === userId);
        if (categoryIndex === -1) {
            throw new common_1.NotFoundException(`Category with ID ${id} not found`);
        }
        this.categories[categoryIndex] = {
            ...this.categories[categoryIndex],
            ...updateCategoryDto,
            updatedAt: new Date(),
        };
        return this.categories[categoryIndex];
    }
    async remove(id, userId) {
        const categoryIndex = this.categories.findIndex(c => c.id === id && c.userId === userId);
        if (categoryIndex === -1) {
            throw new common_1.NotFoundException(`Category with ID ${id} not found`);
        }
        this.categories.splice(categoryIndex, 1);
    }
    async findQuickAccess(userId) {
        return this.categories.filter(c => c.isQuickAccess && c.userId === userId);
    }
};
exports.CategoriesService = CategoriesService;
exports.CategoriesService = CategoriesService = __decorate([
    (0, common_1.Injectable)()
], CategoriesService);
//# sourceMappingURL=categories.service.js.map