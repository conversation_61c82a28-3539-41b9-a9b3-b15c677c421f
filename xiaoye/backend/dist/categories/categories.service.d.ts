import { CreateCategoryDto, UpdateCategoryDto } from './dto/category.dto';
import { Category } from '../types';
export declare class CategoriesService {
    private categories;
    findAll(userId?: string): Promise<Category[]>;
    findOne(id: string): Promise<Category>;
    create(userId: string, createCategoryDto: CreateCategoryDto): Promise<Category>;
    update(id: string, userId: string, updateCategoryDto: UpdateCategoryDto): Promise<Category>;
    remove(id: string, userId: string): Promise<void>;
    findQuickAccess(userId: string): Promise<Category[]>;
}
