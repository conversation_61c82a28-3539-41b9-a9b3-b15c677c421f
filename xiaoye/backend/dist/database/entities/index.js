"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Bookmark = exports.Category = exports.UserRole = exports.User = void 0;
var user_entity_1 = require("./user.entity");
Object.defineProperty(exports, "User", { enumerable: true, get: function () { return user_entity_1.User; } });
Object.defineProperty(exports, "UserRole", { enumerable: true, get: function () { return user_entity_1.UserRole; } });
var category_entity_1 = require("./category.entity");
Object.defineProperty(exports, "Category", { enumerable: true, get: function () { return category_entity_1.Category; } });
var bookmark_entity_1 = require("./bookmark.entity");
Object.defineProperty(exports, "Bookmark", { enumerable: true, get: function () { return bookmark_entity_1.Bookmark; } });
//# sourceMappingURL=index.js.map