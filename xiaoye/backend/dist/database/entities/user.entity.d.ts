import { Category } from './category.entity';
import { Bookmark } from './bookmark.entity';
export declare enum UserRole {
    USER = "user",
    ADMIN = "admin"
}
export declare class User {
    id: string;
    email: string;
    name: string;
    avatar: string;
    password: string;
    role: UserRole;
    isActive: boolean;
    refreshToken: string | null;
    createdAt: Date;
    updatedAt: Date;
    categories: Category[];
    bookmarks: Bookmark[];
}
