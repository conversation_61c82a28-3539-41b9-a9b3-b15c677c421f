import { User } from './user.entity';
import { Category } from './category.entity';
export declare class Bookmark {
    id: string;
    title: string;
    url: string;
    description: string;
    icon: string;
    favicon: string;
    sortOrder: number;
    isVisible: boolean;
    isQuickAccess: boolean;
    userId: string;
    categoryId: string;
    createdAt: Date;
    updatedAt: Date;
    user: User;
    category: Category;
}
