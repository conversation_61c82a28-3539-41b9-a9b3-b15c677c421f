"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BookmarksService = void 0;
const common_1 = require("@nestjs/common");
let BookmarksService = class BookmarksService {
    bookmarks = [
        {
            id: '1',
            title: 'Google',
            url: 'https://www.google.com',
            description: '搜索引擎',
            favicon: 'https://www.google.com/favicon.ico',
            sortOrder: 1,
            isVisible: true,
            isQuickAccess: true,
            userId: 'user1',
            categoryId: 'cat1',
            createdAt: new Date(),
            updatedAt: new Date(),
        },
        {
            id: '2',
            title: 'GitHub',
            url: 'https://github.com',
            description: '代码托管平台',
            favicon: 'https://github.com/favicon.ico',
            sortOrder: 2,
            isVisible: true,
            isQuickAccess: false,
            userId: 'user1',
            categoryId: 'cat2',
            createdAt: new Date(),
            updatedAt: new Date(),
        },
    ];
    async findAll(userId) {
        if (userId) {
            return this.bookmarks.filter(bookmark => bookmark.userId === userId);
        }
        return this.bookmarks;
    }
    async findOne(id) {
        const bookmark = this.bookmarks.find(b => b.id === id);
        if (!bookmark) {
            throw new common_1.NotFoundException(`Bookmark with ID ${id} not found`);
        }
        return bookmark;
    }
    async create(userId, createBookmarkDto) {
        const newBookmark = {
            id: Date.now().toString(),
            ...createBookmarkDto,
            userId,
            sortOrder: createBookmarkDto.sortOrder ?? 0,
            isVisible: createBookmarkDto.isVisible ?? true,
            isQuickAccess: createBookmarkDto.isQuickAccess ?? false,
            createdAt: new Date(),
            updatedAt: new Date(),
        };
        this.bookmarks.push(newBookmark);
        return newBookmark;
    }
    async update(id, userId, updateBookmarkDto) {
        const bookmarkIndex = this.bookmarks.findIndex(b => b.id === id && b.userId === userId);
        if (bookmarkIndex === -1) {
            throw new common_1.NotFoundException(`Bookmark with ID ${id} not found`);
        }
        this.bookmarks[bookmarkIndex] = {
            ...this.bookmarks[bookmarkIndex],
            ...updateBookmarkDto,
            updatedAt: new Date(),
        };
        return this.bookmarks[bookmarkIndex];
    }
    async remove(id, userId) {
        const bookmarkIndex = this.bookmarks.findIndex(b => b.id === id && b.userId === userId);
        if (bookmarkIndex === -1) {
            throw new common_1.NotFoundException(`Bookmark with ID ${id} not found`);
        }
        this.bookmarks.splice(bookmarkIndex, 1);
    }
    async findByCategory(categoryId, userId) {
        return this.bookmarks.filter(b => b.categoryId === categoryId && b.userId === userId);
    }
    async findQuickAccess(userId) {
        return this.bookmarks.filter(b => b.isQuickAccess && b.userId === userId);
    }
};
exports.BookmarksService = BookmarksService;
exports.BookmarksService = BookmarksService = __decorate([
    (0, common_1.Injectable)()
], BookmarksService);
//# sourceMappingURL=bookmarks.service.js.map