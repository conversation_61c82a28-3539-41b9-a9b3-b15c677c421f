import { CreateBookmarkDto, UpdateBookmarkDto } from './dto/bookmark.dto';
import { Bookmark } from '../types';
export declare class BookmarksService {
    private bookmarks;
    findAll(userId?: string): Promise<Bookmark[]>;
    findOne(id: string): Promise<Bookmark>;
    create(userId: string, createBookmarkDto: CreateBookmarkDto): Promise<Bookmark>;
    update(id: string, userId: string, updateBookmarkDto: UpdateBookmarkDto): Promise<Bookmark>;
    remove(id: string, userId: string): Promise<void>;
    findByCategory(categoryId: string, userId: string): Promise<Bookmark[]>;
    findQuickAccess(userId: string): Promise<Bookmark[]>;
}
