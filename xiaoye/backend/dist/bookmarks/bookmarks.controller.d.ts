import { BookmarksService } from './bookmarks.service';
import { CreateBookmarkDto, UpdateBookmarkDto } from './dto/bookmark.dto';
export declare class BookmarksController {
    private readonly bookmarksService;
    constructor(bookmarksService: BookmarksService);
    create(createBookmarkDto: CreateBookmarkDto, userId?: string): Promise<import("../types").Bookmark>;
    findAll(userId?: string): Promise<import("../types").Bookmark[]>;
    findQuickAccess(userId?: string): Promise<import("../types").Bookmark[]>;
    findByCategory(categoryId: string, userId?: string): Promise<import("../types").Bookmark[]>;
    findOne(id: string): Promise<import("../types").Bookmark>;
    update(id: string, updateBookmarkDto: UpdateBookmarkDto, userId?: string): Promise<import("../types").Bookmark>;
    remove(id: string, userId?: string): Promise<void>;
}
