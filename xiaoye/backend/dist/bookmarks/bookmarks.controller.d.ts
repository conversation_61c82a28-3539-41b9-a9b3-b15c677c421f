import { BookmarksService } from './bookmarks.service';
import { CreateBookmarkDto, UpdateBookmarkDto } from './dto/bookmark.dto';
import { Bookmark } from '../types';
export declare class BookmarksController {
    private readonly bookmarksService;
    constructor(bookmarksService: BookmarksService);
    create(createBookmarkDto: CreateBookmarkDto, userId?: string): Promise<Bookmark>;
    findAll(userId?: string): Promise<Bookmark[]>;
    findQuickAccess(userId?: string): Promise<Bookmark[]>;
    findByCategory(categoryId: string, userId?: string): Promise<Bookmark[]>;
    findOne(id: string): Promise<Bookmark>;
    update(id: string, updateBookmarkDto: UpdateBookmarkDto, userId?: string): Promise<Bookmark>;
    remove(id: string, userId?: string): Promise<void>;
}
