{"version": 3, "file": "IsBtcAddress.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsBtcAddress.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,qBAAqB,MAAM,4BAA4B,CAAC;AAE/D,MAAM,CAAC,IAAM,cAAc,GAAG,cAAc,CAAC;AAE7C;;;GAGG;AACH,MAAM,UAAU,YAAY,CAAC,KAAc;IACzC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,qBAAqB,CAAC,KAAK,CAAC,CAAC;AACnE,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,YAAY,CAAC,iBAAqC;IAChE,OAAO,UAAU,CACf;QACE,IAAI,EAAE,cAAc;QACpB,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,YAAY,CAAC,KAAK,CAAC,EAAnB,CAAmB;YACvD,cAAc,EAAE,YAAY,CAAC,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,iCAAiC,EAA9C,CAA8C,EAAE,iBAAiB,CAAC;SAC9G;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isBtcAddressValidator from 'validator/lib/isBtcAddress';\n\nexport const IS_BTC_ADDRESS = 'isBtcAddress';\n\n/**\n * Check if the string is a valid BTC address.\n * If given value is not a string, then it returns false.\n */\nexport function isBtcAddress(value: unknown): boolean {\n  return typeof value === 'string' && isBtcAddressValidator(value);\n}\n\n/**\n * Check if the string is a valid BTC address.\n * If given value is not a string, then it returns false.\n */\nexport function IsBtcAddress(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_BTC_ADDRESS,\n      validator: {\n        validate: (value, args): boolean => isBtcAddress(value),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be a BTC address', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}