{"version": 3, "file": "IsDateString.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsDateString.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAEhE,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AAExC,MAAM,CAAC,IAAM,cAAc,GAAG,cAAc,CAAC;AAE7C;;GAEG;AACH,MAAM,UAAU,YAAY,CAAC,KAAc,EAAE,OAAsC;IACjF,OAAO,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACnC,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,YAAY,CAC1B,OAAsC,EACtC,iBAAqC;IAErC,OAAO,UAAU,CACf;QACE,IAAI,EAAE,cAAc;QACpB,WAAW,EAAE,CAAC,OAAO,CAAC;QACtB,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,IAAc,OAAA,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,EAA5B,CAA4B;YAC1D,cAAc,EAAE,YAAY,CAC1B,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,gDAAgD,EAA7D,CAA6D,EAC3E,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport * as ValidatorJS from 'validator';\nimport { isISO8601 } from './IsISO8601';\n\nexport const IS_DATE_STRING = 'isDateString';\n\n/**\n * Alias for IsISO8601 validator\n */\nexport function isDateString(value: unknown, options?: ValidatorJS.IsISO8601Options): boolean {\n  return isISO8601(value, options);\n}\n\n/**\n * Alias for IsISO8601 validator\n */\nexport function IsDateString(\n  options?: ValidatorJS.IsISO8601Options,\n  validationOptions?: ValidationOptions\n): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_DATE_STRING,\n      constraints: [options],\n      validator: {\n        validate: (value): boolean => isDateString(value, options),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be a valid ISO 8601 date string',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}