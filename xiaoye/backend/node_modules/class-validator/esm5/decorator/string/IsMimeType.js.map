{"version": 3, "file": "IsMimeType.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsMimeType.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,mBAAmB,MAAM,0BAA0B,CAAC;AAE3D,MAAM,CAAC,IAAM,YAAY,GAAG,YAAY,CAAC;AAEzC;;;GAGG;AACH,MAAM,UAAU,UAAU,CAAC,KAAc;IACvC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,mBAAmB,CAAC,KAAK,CAAC,CAAC;AACjE,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,UAAU,CAAC,iBAAqC;IAC9D,OAAO,UAAU,CACf;QACE,IAAI,EAAE,YAAY;QAClB,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,UAAU,CAAC,KAAK,CAAC,EAAjB,CAAiB;YACrD,cAAc,EAAE,YAAY,CAC1B,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,oCAAoC,EAAjD,CAAiD,EAC/D,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isMimeTypeValidator from 'validator/lib/isMimeType';\n\nexport const IS_MIME_TYPE = 'isMimeType';\n\n/**\n * Check if the string matches to a valid MIME type format\n * If given value is not a string, then it returns false.\n */\nexport function isMimeType(value: unknown): boolean {\n  return typeof value === 'string' && isMimeTypeValidator(value);\n}\n\n/**\n * Check if the string matches to a valid MIME type format\n * If given value is not a string, then it returns false.\n */\nexport function IsMimeType(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_MIME_TYPE,\n      validator: {\n        validate: (value, args): boolean => isMimeType(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be MIME type format',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}