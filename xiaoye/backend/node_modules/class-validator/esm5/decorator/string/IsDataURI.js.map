{"version": 3, "file": "IsDataURI.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsDataURI.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,kBAAkB,MAAM,yBAAyB,CAAC;AAEzD,MAAM,CAAC,IAAM,WAAW,GAAG,WAAW,CAAC;AAEvC;;;GAGG;AACH,MAAM,UAAU,SAAS,CAAC,KAAc;IACtC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,kBAAkB,CAAC,KAAK,CAAC,CAAC;AAChE,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,SAAS,CAAC,iBAAqC;IAC7D,OAAO,UAAU,CACf;QACE,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,SAAS,CAAC,KAAK,CAAC,EAAhB,CAAgB;YACpD,cAAc,EAAE,YAAY,CAC1B,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,qCAAqC,EAAlD,CAAkD,EAChE,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isDataURIValidator from 'validator/lib/isDataURI';\n\nexport const IS_DATA_URI = 'isDataURI';\n\n/**\n * Check if the string is a data uri format.\n * If given value is not a string, then it returns false.\n */\nexport function isDataURI(value: unknown): boolean {\n  return typeof value === 'string' && isDataURIValidator(value);\n}\n\n/**\n * Check if the string is a data uri format.\n * If given value is not a string, then it returns false.\n */\nexport function IsDataURI(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_DATA_URI,\n      validator: {\n        validate: (value, args): boolean => isDataURI(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be a data uri format',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}