{"version": 3, "file": "Length.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/Length.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,iBAAiB,MAAM,wBAAwB,CAAC;AAEvD,MAAM,CAAC,IAAM,SAAS,GAAG,UAAU,CAAC;AAEpC;;;GAGG;AACH,MAAM,UAAU,MAAM,CAAC,KAAc,EAAE,GAAW,EAAE,GAAY;IAC9D,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,iBAAiB,CAAC,KAAK,EAAE,EAAE,GAAG,KAAA,EAAE,GAAG,KAAA,EAAE,CAAC,CAAC;AAC7E,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,MAAM,CAAC,GAAW,EAAE,GAAY,EAAE,iBAAqC;IACrF,OAAO,UAAU,CACf;QACE,IAAI,EAAE,SAAS;QACf,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;QACvB,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,MAAM,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAzD,CAAyD;YAC7F,cAAc,EAAE,YAAY,CAAC,UAAC,UAAU,EAAE,IAAI;gBAC5C,IAAM,WAAW,GAAG,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,MAAK,IAAI,IAAI,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,MAAK,SAAS,CAAC;gBACxF,IAAM,WAAW,GAAG,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,MAAK,IAAI,IAAI,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,MAAK,SAAS,CAAC;gBACxF,IAAI,WAAW,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAA,CAAC,EAAE,CAAC;oBAC7E,OAAO,UAAU,GAAG,mEAAmE,CAAC;gBAC1F,CAAC;qBAAM,IAAI,WAAW,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAA,EAAE,CAAC;oBACnE,OAAO,UAAU,GAAG,oEAAoE,CAAC;gBAC3F,CAAC;gBACD,OAAO,CACL,UAAU;oBACV,6GAA6G,CAC9G,CAAC;YACJ,CAAC,EAAE,iBAAiB,CAAC;SACtB;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isLengthValidator from 'validator/lib/isLength';\n\nexport const IS_LENGTH = 'isLength';\n\n/**\n * Checks if the string's length falls in a range. Note: this function takes into account surrogate pairs.\n * If given value is not a string, then it returns false.\n */\nexport function length(value: unknown, min: number, max?: number): boolean {\n  return typeof value === 'string' && isLengthValidator(value, { min, max });\n}\n\n/**\n * Checks if the string's length falls in a range. Note: this function takes into account surrogate pairs.\n * If given value is not a string, then it returns false.\n */\nexport function Length(min: number, max?: number, validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_LENGTH,\n      constraints: [min, max],\n      validator: {\n        validate: (value, args): boolean => length(value, args?.constraints[0], args?.constraints[1]),\n        defaultMessage: buildMessage((eachPrefix, args) => {\n          const isMinLength = args?.constraints[0] !== null && args?.constraints[0] !== undefined;\n          const isMaxLength = args?.constraints[1] !== null && args?.constraints[1] !== undefined;\n          if (isMinLength && (!args.value || args.value.length < args?.constraints[0])) {\n            return eachPrefix + '$property must be longer than or equal to $constraint1 characters';\n          } else if (isMaxLength && args.value.length > args?.constraints[1]) {\n            return eachPrefix + '$property must be shorter than or equal to $constraint2 characters';\n          }\n          return (\n            eachPrefix +\n            '$property must be longer than or equal to $constraint1 and shorter than or equal to $constraint2 characters'\n          );\n        }, validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}