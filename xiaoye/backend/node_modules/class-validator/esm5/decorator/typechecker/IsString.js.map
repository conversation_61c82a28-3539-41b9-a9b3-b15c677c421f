{"version": 3, "file": "IsString.js", "sourceRoot": "", "sources": ["../../../../src/decorator/typechecker/IsString.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAEhE,MAAM,CAAC,IAAM,SAAS,GAAG,UAAU,CAAC;AAEpC;;GAEG;AACH,MAAM,UAAU,QAAQ,CAAC,KAAc;IACrC,OAAO,KAAK,YAAY,MAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,CAAC;AAC9D,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,QAAQ,CAAC,iBAAqC;IAC5D,OAAO,UAAU,CACf;QACE,IAAI,EAAE,SAAS;QACf,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,QAAQ,CAAC,KAAK,CAAC,EAAf,CAAe;YACnD,cAAc,EAAE,YAAY,CAAC,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,4BAA4B,EAAzC,CAAyC,EAAE,iBAAiB,CAAC;SACzG;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\n\nexport const IS_STRING = 'isString';\n\n/**\n * Checks if a given value is a real string.\n */\nexport function isString(value: unknown): value is string {\n  return value instanceof String || typeof value === 'string';\n}\n\n/**\n * Checks if a given value is a real string.\n */\nexport function IsString(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_STRING,\n      validator: {\n        validate: (value, args): boolean => isString(value),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be a string', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}