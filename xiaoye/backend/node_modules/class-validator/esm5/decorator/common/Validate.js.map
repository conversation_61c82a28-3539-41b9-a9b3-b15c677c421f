{"version": 3, "file": "Validate.js", "sourceRoot": "", "sources": ["../../../../src/decorator/common/Validate.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,kBAAkB,EAAE,MAAM,mCAAmC,CAAC;AACvE,OAAO,EAAE,kBAAkB,EAAE,MAAM,gCAAgC,CAAC;AACpE,OAAO,EAAE,eAAe,EAAE,MAAM,kCAAkC,CAAC;AACnE,OAAO,EAAE,kBAAkB,EAAE,MAAM,mCAAmC,CAAC;AAEvE;;GAEG;AACH,MAAM,UAAU,mBAAmB,CAAC,OAA4C;IAC9E,OAAO,UAAU,MAAgB;QAC/B,IAAM,OAAO,GAAG,OAAO,IAAI,OAAO,CAAC,KAAK,CAAC;QACzC,IAAI,IAAI,GAAG,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;QACvD,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,IAAI,GAAI,MAAc,CAAC,IAAI,CAAC;YAC5B,IAAI,CAAC,IAAI;gBACP,oCAAoC;gBACpC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,GAAG,GAAI,CAAY,CAAC,WAAW,EAAE,EAAjC,CAAiC,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QACvG,CAAC;QACD,IAAM,QAAQ,GAAG,IAAI,kBAAkB,CAAC,MAAM,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QAC/D,kBAAkB,EAAE,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;IACvD,CAAC,CAAC;AACJ,CAAC;AAYD,MAAM,UAAU,QAAQ,CACtB,eAAyB,EACzB,8BAA0D,EAC1D,sBAA0C;IAE1C,OAAO,UAAU,MAAc,EAAE,YAAoB;QACnD,IAAM,IAAI,GAA2B;YACnC,IAAI,EAAE,eAAe,CAAC,iBAAiB;YACvC,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,aAAa,EAAE,eAAe;YAC9B,WAAW,EAAE,KAAK,CAAC,OAAO,CAAC,8BAA8B,CAAC,CAAC,CAAC,CAAC,8BAA8B,CAAC,CAAC,CAAC,SAAS;YACvG,iBAAiB,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,8BAA8B,CAAC;gBAC/D,CAAC,CAAC,8BAA8B;gBAChC,CAAC,CAAC,sBAAsB;SAC3B,CAAC;QACF,kBAAkB,EAAE,CAAC,qBAAqB,CAAC,IAAI,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3E,CAAC,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { ValidationMetadataArgs } from '../../metadata/ValidationMetadataArgs';\nimport { ValidationMetadata } from '../../metadata/ValidationMetadata';\nimport { getMetadataStorage } from '../../metadata/MetadataStorage';\nimport { ValidationTypes } from '../../validation/ValidationTypes';\nimport { ConstraintMetadata } from '../../metadata/ConstraintMetadata';\n\n/**\n * Registers custom validator class.\n */\nexport function ValidatorConstraint(options?: { name?: string; async?: boolean }) {\n  return function (target: Function): void {\n    const isAsync = options && options.async;\n    let name = options && options.name ? options.name : '';\n    if (!name) {\n      name = (target as any).name;\n      if (!name)\n        // generate name if it was not given\n        name = name.replace(/\\.?([A-Z]+)/g, (x, y) => '_' + (y as string).toLowerCase()).replace(/^_/, '');\n    }\n    const metadata = new ConstraintMetadata(target, name, isAsync);\n    getMetadataStorage().addConstraintMetadata(metadata);\n  };\n}\n\n/**\n * Performs validation based on the given custom validation class.\n * Validation class must be decorated with ValidatorConstraint decorator.\n */\nexport function Validate(constraintClass: Function, validationOptions?: ValidationOptions): PropertyDecorator;\nexport function Validate(\n  constraintClass: Function,\n  constraints?: any[],\n  validationOptions?: ValidationOptions\n): PropertyDecorator;\nexport function Validate(\n  constraintClass: Function,\n  constraintsOrValidationOptions?: any[] | ValidationOptions,\n  maybeValidationOptions?: ValidationOptions\n): PropertyDecorator {\n  return function (object: object, propertyName: string): void {\n    const args: ValidationMetadataArgs = {\n      type: ValidationTypes.CUSTOM_VALIDATION,\n      target: object.constructor,\n      propertyName: propertyName,\n      constraintCls: constraintClass,\n      constraints: Array.isArray(constraintsOrValidationOptions) ? constraintsOrValidationOptions : undefined,\n      validationOptions: !Array.isArray(constraintsOrValidationOptions)\n        ? constraintsOrValidationOptions\n        : maybeValidationOptions,\n    };\n    getMetadataStorage().addValidationMetadata(new ValidationMetadata(args));\n  };\n}\n"]}