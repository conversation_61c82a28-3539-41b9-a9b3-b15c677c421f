{"version": 3, "file": "register-decorator.js", "sourceRoot": "", "sources": ["../../src/register-decorator.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,kBAAkB,EAAE,MAAM,+BAA+B,CAAC;AAEnE,OAAO,EAAE,kBAAkB,EAAE,MAAM,+BAA+B,CAAC;AAEnE,OAAO,EAAE,eAAe,EAAE,MAAM,8BAA8B,CAAC;AAE/D,OAAO,EAAE,gBAAgB,EAAE,MAAM,aAAa,CAAC;AAC/C,OAAO,EAAE,eAAe,EAAE,kBAAkB,EAAE,MAAM,4BAA4B,CAAC;AAwCjF;;GAEG;AACH,MAAM,UAAU,iBAAiB,CAAC,OAAmC;IACnE,IAAI,aAAuB,CAAC;IAC5B,IAAI,OAAO,CAAC,SAAS,YAAY,QAAQ,EAAE,CAAC;QAC1C,aAAa,GAAG,OAAO,CAAC,SAAS,CAAC;QAClC,IAAM,iBAAiB,GAAG,gBAAgB,CAAC,eAAe,CAAC,CAAC,6BAA6B,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAC7G,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACjC,MAAM,+FAAwF,OAAO,CAAC,MAAM,CAAC,IAAI,cAAI,OAAO,CAAC,YAAY,CAAE,CAAC;QAC9I,CAAC;IACH,CAAC;SAAM,CAAC;QACN,IAAM,WAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QACpC,aAAa;YAAG;YAYhB,CAAC;YAXC,mCAAQ,GAAR,UAAS,KAAU,EAAE,mBAAyC;gBAC5D,OAAO,WAAS,CAAC,QAAQ,CAAC,KAAK,EAAE,mBAAmB,CAAC,CAAC;YACxD,CAAC;YAED,yCAAc,GAAd,UAAe,mBAAyC;gBACtD,IAAI,WAAS,CAAC,cAAc,EAAE,CAAC;oBAC7B,OAAO,WAAS,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC;gBACvD,CAAC;gBAED,OAAO,EAAE,CAAC;YACZ,CAAC;YACH,uBAAC;QAAD,CAAC,AAZe,GAYf,CAAC;QACF,kBAAkB,EAAE,CAAC,qBAAqB,CAAC,IAAI,kBAAkB,CAAC,aAAa,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;IACjH,CAAC;IAED,IAAM,sBAAsB,GAA2B;QACrD,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,eAAe,CAAC,iBAAiB;QAC9G,IAAI,EAAE,OAAO,CAAC,IAAI;QAClB,MAAM,EAAE,OAAO,CAAC,MAAM;QACtB,YAAY,EAAE,OAAO,CAAC,YAAY;QAClC,iBAAiB,EAAE,OAAO,CAAC,OAAO;QAClC,aAAa,EAAE,aAAa;QAC5B,WAAW,EAAE,OAAO,CAAC,WAAW;KACjC,CAAC;IACF,kBAAkB,EAAE,CAAC,qBAAqB,CAAC,IAAI,kBAAkB,CAAC,sBAAsB,CAAC,CAAC,CAAC;AAC7F,CAAC", "sourcesContent": ["import { ConstraintMetadata } from './metadata/ConstraintMetadata';\nimport { ValidatorConstraintInterface } from './validation/ValidatorConstraintInterface';\nimport { ValidationMetadata } from './metadata/ValidationMetadata';\nimport { ValidationMetadataArgs } from './metadata/ValidationMetadataArgs';\nimport { ValidationTypes } from './validation/ValidationTypes';\nimport { ValidationArguments } from './validation/ValidationArguments';\nimport { getFromContainer } from './container';\nimport { MetadataStorage, getMetadataStorage } from './metadata/MetadataStorage';\nimport { ValidationOptions } from './decorator/ValidationOptions';\n\nexport interface ValidationDecoratorOptions {\n  /**\n   * Target object to be validated.\n   */\n  target: Function;\n\n  /**\n   * Target object's property name to be validated.\n   */\n  propertyName: string;\n\n  /**\n   * Name of the validation that is being registered.\n   */\n  name?: string;\n\n  /**\n   * Indicates if this decorator will perform async validation.\n   */\n  async?: boolean;\n\n  /**\n   * Validator options.\n   */\n  options?: ValidationOptions;\n\n  /**\n   * Array of validation constraints.\n   */\n  constraints?: any[];\n\n  /**\n   * Validator that performs validation.\n   */\n  validator: ValidatorConstraintInterface | Function;\n}\n\n/**\n * Registers a custom validation decorator.\n */\nexport function registerDecorator(options: ValidationDecoratorOptions): void {\n  let constraintCls: Function;\n  if (options.validator instanceof Function) {\n    constraintCls = options.validator;\n    const constraintClasses = getFromContainer(MetadataStorage).getTargetValidatorConstraints(options.validator);\n    if (constraintClasses.length > 1) {\n      throw `More than one implementation of ValidatorConstraintInterface found for validator on: ${options.target.name}:${options.propertyName}`;\n    }\n  } else {\n    const validator = options.validator;\n    constraintCls = class CustomConstraint implements ValidatorConstraintInterface {\n      validate(value: any, validationArguments?: ValidationArguments): Promise<boolean> | boolean {\n        return validator.validate(value, validationArguments);\n      }\n\n      defaultMessage(validationArguments?: ValidationArguments): string {\n        if (validator.defaultMessage) {\n          return validator.defaultMessage(validationArguments);\n        }\n\n        return '';\n      }\n    };\n    getMetadataStorage().addConstraintMetadata(new ConstraintMetadata(constraintCls, options.name, options.async));\n  }\n\n  const validationMetadataArgs: ValidationMetadataArgs = {\n    type: options.name && ValidationTypes.isValid(options.name) ? options.name : ValidationTypes.CUSTOM_VALIDATION,\n    name: options.name,\n    target: options.target,\n    propertyName: options.propertyName,\n    validationOptions: options.options,\n    constraintCls: constraintCls,\n    constraints: options.constraints,\n  };\n  getMetadataStorage().addValidationMetadata(new ValidationMetadata(validationMetadataArgs));\n}\n"]}