import { ValidationOptions } from '../ValidationOptions';
export declare const MIN = "min";
/**
 * Checks if the first number is greater than or equal to the second.
 */
export declare function min(num: unknown, min: number): boolean;
/**
 * Checks if the value is greater than or equal to the allowed minimum value.
 */
export declare function Min(minValue: number, validationOptions?: ValidationOptions): PropertyDecorator;
