import { ValidationOptions } from '../ValidationOptions';
export declare const IS_SURROGATE_PAIR = "isSurrogatePair";
/**
 * Checks if the string contains any surrogate pairs chars.
 * If given value is not a string, then it returns false.
 */
export declare function isSurrogatePair(value: unknown): boolean;
/**
 * Checks if the string contains any surrogate pairs chars.
 * If given value is not a string, then it returns false.
 */
export declare function IsSurrogatePair(validationOptions?: ValidationOptions): PropertyDecorator;
