import { ValidationOptions } from '../ValidationOptions';
export declare const IS_HEXADECIMAL = "isHexadecimal";
/**
 * Checks if the string is a hexadecimal number.
 * If given value is not a string, then it returns false.
 */
export declare function isHexadecimal(value: unknown): boolean;
/**
 * Checks if the string is a hexadecimal number.
 * If given value is not a string, then it returns false.
 */
export declare function IsHexadecimal(validationOptions?: ValidationOptions): PropertyDecorator;
