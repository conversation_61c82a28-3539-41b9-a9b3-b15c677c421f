import { ValidationOptions } from '../ValidationOptions';
export declare const IS_ASCII = "isAscii";
/**
 * Checks if the string contains ASCII chars only.
 * If given value is not a string, then it returns false.
 */
export declare function isAscii(value: unknown): boolean;
/**
 * Checks if the string contains ASCII chars only.
 * If given value is not a string, then it returns false.
 */
export declare function IsAscii(validationOptions?: ValidationOptions): PropertyDecorator;
