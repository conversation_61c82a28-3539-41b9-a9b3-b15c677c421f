import { ValidationOptions } from '../ValidationOptions';
export declare const IS_TIMEZONE = "isTimeZone";
/**
 * Checks if the string represents a valid IANA timezone
 * If the given value is not a valid IANA timezone, then it returns false.
 */
export declare function isTimeZone(value: unknown): boolean;
/**
 * Checks if the string represents a valid IANA timezone
 * If the given value is not a valid IANA timezone, then it returns false.
 */
export declare function IsTimeZone(validationOptions?: ValidationOptions): PropertyDecorator;
