{"version": 3, "file": "ValidateBy.js", "sourceRoot": "", "sources": ["../../../../src/decorator/common/ValidateBy.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,iBAAiB,EAAE,MAAM,0BAA0B,CAAC;AAW7D,MAAM,UAAU,YAAY,CAC1B,IAAgE,EAChE,iBAAqC;IAErC,OAAO,CAAC,mBAAyC,EAAU,EAAE;QAC3D,MAAM,UAAU,GAAG,iBAAiB,IAAI,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC;QACvF,OAAO,IAAI,CAAC,UAAU,EAAE,mBAAmB,CAAC,CAAC;IAC/C,CAAC,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,UAAU,CAAC,OAA0B,EAAE,iBAAqC;IAC1F,OAAO,UAAU,MAAc,EAAE,YAAoB;QACnD,iBAAiB,CAAC;YAChB,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,OAAO,EAAE,iBAAiB;YAC1B,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,SAAS,EAAE,OAAO,CAAC,SAAS;SAC7B,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { registerDecorator } from '../../register-decorator';\nimport { ValidationArguments } from '../../validation/ValidationArguments';\nimport { ValidatorConstraintInterface } from '../../validation/ValidatorConstraintInterface';\n\nexport interface ValidateByOptions {\n  name: string;\n  constraints?: any[];\n  validator: ValidatorConstraintInterface | Function;\n  async?: boolean;\n}\n\nexport function buildMessage(\n  impl: (eachPrefix: string, args?: ValidationArguments) => string,\n  validationOptions?: ValidationOptions\n): (validationArguments?: ValidationArguments) => string {\n  return (validationArguments?: ValidationArguments): string => {\n    const eachPrefix = validationOptions && validationOptions.each ? 'each value in ' : '';\n    return impl(eachPrefix, validationArguments);\n  };\n}\n\nexport function ValidateBy(options: ValidateByOptions, validationOptions?: ValidationOptions): PropertyDecorator {\n  return function (object: object, propertyName: string): void {\n    registerDecorator({\n      name: options.name,\n      target: object.constructor,\n      propertyName: propertyName,\n      options: validationOptions,\n      constraints: options.constraints,\n      validator: options.validator,\n    });\n  };\n}\n"]}