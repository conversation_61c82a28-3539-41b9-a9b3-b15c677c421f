{"version": 3, "file": "IsFQDN.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsFQDN.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,eAAe,MAAM,sBAAsB,CAAC;AAGnD,MAAM,CAAC,MAAM,OAAO,GAAG,QAAQ,CAAC;AAEhC;;;GAGG;AACH,MAAM,UAAU,MAAM,CAAC,KAAc,EAAE,OAAmC;IACxE,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,eAAe,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACtE,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,MAAM,CAAC,OAAmC,EAAE,iBAAqC;IAC/F,OAAO,UAAU,CACf;QACE,IAAI,EAAE,OAAO;QACb,WAAW,EAAE,CAAC,OAAO,CAAC;QACtB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YACvE,cAAc,EAAE,YAAY,CAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,uCAAuC,EAClE,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isFqdnValidator from 'validator/lib/isFQDN';\nimport * as ValidatorJS from 'validator';\n\nexport const IS_FQDN = 'isFqdn';\n\n/**\n * Checks if the string is a fully qualified domain name (e.g. domain.com).\n * If given value is not a string, then it returns false.\n */\nexport function isFQDN(value: unknown, options?: ValidatorJS.IsFQDNOptions): boolean {\n  return typeof value === 'string' && isFqdnValidator(value, options);\n}\n\n/**\n * Checks if the string is a fully qualified domain name (e.g. domain.com).\n * If given value is not a string, then it returns false.\n */\nexport function IsFQDN(options?: ValidatorJS.IsFQDNOptions, validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_FQDN,\n      constraints: [options],\n      validator: {\n        validate: (value, args): boolean => isFQDN(value, args?.constraints[0]),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be a valid domain name',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}