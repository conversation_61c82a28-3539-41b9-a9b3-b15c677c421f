{"version": 3, "file": "IsMacAddress.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsMacAddress.ts"], "names": [], "mappings": "AAAA,OAAO,EAAqB,mBAAmB,EAAE,MAAM,sBAAsB,CAAC;AAC9E,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,qBAAqB,MAAM,4BAA4B,CAAC;AAG/D,MAAM,CAAC,MAAM,cAAc,GAAG,cAAc,CAAC;AAE7C;;;GAGG;AACH,MAAM,UAAU,YAAY,CAAC,KAAc,EAAE,OAAyC;IACpF,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,qBAAqB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AAC5E,CAAC;AAWD,MAAM,UAAU,YAAY,CAC1B,6BAAmF,EACnF,oBAAwC;IAExC,MAAM,OAAO,GAAG,CAAC,mBAAmB,CAAC,6BAA6B,CAAC,CAAC,CAAC,CAAC,6BAA6B,CAAC,CAAC,CAAC,SAAS,CAAC;IAChH,MAAM,iBAAiB,GAAG,mBAAmB,CAAC,6BAA6B,CAAC;QAC1E,CAAC,CAAC,6BAA6B;QAC/B,CAAC,CAAC,oBAAoB,CAAC;IAEzB,OAAO,UAAU,CACf;QACE,IAAI,EAAE,cAAc;QACpB,WAAW,EAAE,CAAC,OAAO,CAAC;QACtB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC;YAChE,cAAc,EAAE,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,iCAAiC,EAAE,iBAAiB,CAAC;SAC9G;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions, isValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isMacAddressValidator from 'validator/lib/isMACAddress';\nimport * as ValidatorJS from 'validator';\n\nexport const IS_MAC_ADDRESS = 'isMacAddress';\n\n/**\n * Check if the string is a MAC address.\n * If given value is not a string, then it returns false.\n */\nexport function isMACAddress(value: unknown, options?: ValidatorJS.IsMACAddressOptions): boolean {\n  return typeof value === 'string' && isMacAddressValidator(value, options);\n}\n\n/**\n * Check if the string is a MAC address.\n * If given value is not a string, then it returns false.\n */\nexport function IsMACAddress(\n  optionsArg?: ValidatorJS.IsMACAddressOptions,\n  validationOptionsArg?: ValidationOptions\n): PropertyDecorator;\nexport function IsMACAddress(validationOptionsArg?: ValidationOptions): PropertyDecorator;\nexport function IsMACAddress(\n  optionsOrValidationOptionsArg?: ValidatorJS.IsMACAddressOptions | ValidationOptions,\n  validationOptionsArg?: ValidationOptions\n): PropertyDecorator {\n  const options = !isValidationOptions(optionsOrValidationOptionsArg) ? optionsOrValidationOptionsArg : undefined;\n  const validationOptions = isValidationOptions(optionsOrValidationOptionsArg)\n    ? optionsOrValidationOptionsArg\n    : validationOptionsArg;\n\n  return ValidateBy(\n    {\n      name: IS_MAC_ADDRESS,\n      constraints: [options],\n      validator: {\n        validate: (value, args): boolean => isMACAddress(value, options),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be a MAC Address', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}