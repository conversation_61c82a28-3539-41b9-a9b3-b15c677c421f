{"version": 3, "file": "IsISSN.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsISSN.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,eAAe,MAAM,sBAAsB,CAAC;AAGnD,MAAM,CAAC,MAAM,OAAO,GAAG,QAAQ,CAAC;AAEhC;;;GAGG;AACH,MAAM,UAAU,MAAM,CAAC,KAAc,EAAE,OAAmC;IACxE,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,eAAe,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACtE,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,MAAM,CAAC,OAAmC,EAAE,iBAAqC;IAC/F,OAAO,UAAU,CACf;QACE,IAAI,EAAE,OAAO;QACb,WAAW,EAAE,CAAC,OAAO,CAAC;QACtB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YACvE,cAAc,EAAE,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,0BAA0B,EAAE,iBAAiB,CAAC;SACvG;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isISSNValidator from 'validator/lib/isISSN';\nimport * as ValidatorJS from 'validator';\n\nexport const IS_ISSN = 'isISSN';\n\n/**\n * Checks if the string is a ISSN.\n * If given value is not a string, then it returns false.\n */\nexport function isISSN(value: unknown, options?: ValidatorJS.IsISSNOptions): boolean {\n  return typeof value === 'string' && isISSNValidator(value, options);\n}\n\n/**\n * Checks if the string is a ISSN.\n * If given value is not a string, then it returns false.\n */\nexport function IsISSN(options?: ValidatorJS.IsISSNOptions, validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_ISSN,\n      constraints: [options],\n      validator: {\n        validate: (value, args): boolean => isISSN(value, args?.constraints[0]),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be a ISSN', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}