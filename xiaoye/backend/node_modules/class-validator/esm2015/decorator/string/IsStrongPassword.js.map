{"version": 3, "file": "IsStrongPassword.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsStrongPassword.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,SAAS,MAAM,WAAW,CAAC;AAEvC,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAEhE,MAAM,CAAC,MAAM,kBAAkB,GAAG,kBAAkB,CAAC;AAUrD;;;GAGG;AACH,MAAM,UAAU,gBAAgB,CAAC,KAAc,EAAE,OAAiC;IAChF,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,SAAS,CAAC,gBAAgB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACjF,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,gBAAgB,CAC9B,OAAiC,EACjC,iBAAqC;IAErC,OAAO,UAAU,CACf;QACE,IAAI,EAAE,kBAAkB;QACxB,WAAW,EAAE,CAAC,OAAO,CAAC;QACtB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;YAChF,cAAc,EAAE,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,gCAAgC,EAAE,iBAAiB,CAAC;SAC7G;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import * as validator from 'validator';\nimport { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\n\nexport const IS_STRONG_PASSWORD = 'isStrongPassword';\n\n/**\n * Options to be passed to IsStrongPassword decorator.\n */\nexport type IsStrongPasswordOptions = Pick<\n  validator.StrongPasswordOptions,\n  'minLength' | 'minLowercase' | 'minUppercase' | 'minNumbers' | 'minSymbols'\n>;\n\n/**\n * Checks if the string is a strong password.\n * If given value is not a string, then it returns false.\n */\nexport function isStrongPassword(value: unknown, options?: IsStrongPasswordOptions): boolean {\n  return typeof value === 'string' && validator.isStrongPassword(value, options);\n}\n\n/**\n * Checks if the string is a strong password.\n * If given value is not a string, then it returns false.\n */\nexport function IsStrongPassword(\n  options?: IsStrongPasswordOptions,\n  validationOptions?: ValidationOptions\n): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_STRONG_PASSWORD,\n      constraints: [options],\n      validator: {\n        validate: (value, args): boolean => isStrongPassword(value, args.constraints[0]),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property is not strong enough', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}