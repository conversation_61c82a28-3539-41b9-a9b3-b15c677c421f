{"version": 3, "file": "IsOctal.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsOctal.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,gBAAgB,MAAM,uBAAuB,CAAC;AAErD,MAAM,CAAC,MAAM,QAAQ,GAAG,SAAS,CAAC;AAElC;;;GAGG;AACH,MAAM,UAAU,OAAO,CAAC,KAAc;IACpC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,gBAAgB,CAAC,KAAK,CAAC,CAAC;AAC9D,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,OAAO,CAAC,iBAAqC;IAC3D,OAAO,UAAU,CACf;QACE,IAAI,EAAE,QAAQ;QACd,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;YAClD,cAAc,EAAE,YAAY,CAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,sCAAsC,EACjE,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isOctalValidator from 'validator/lib/isOctal';\n\nexport const IS_OCTAL = 'isOctal';\n\n/**\n * Check if the string is a valid octal number.\n * If given value is not a string, then it returns false.\n */\nexport function isOctal(value: unknown): boolean {\n  return typeof value === 'string' && isOctalValidator(value);\n}\n\n/**\n * Check if the string is a valid octal number.\n * If given value is not a string, then it returns false.\n */\nexport function IsOctal(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_OCTAL,\n      validator: {\n        validate: (value, args): boolean => isOctal(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be valid octal number',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}