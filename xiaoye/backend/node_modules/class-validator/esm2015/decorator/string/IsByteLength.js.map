{"version": 3, "file": "IsByteLength.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsByteLength.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,qBAAqB,MAAM,4BAA4B,CAAC;AAE/D,MAAM,CAAC,MAAM,cAAc,GAAG,cAAc,CAAC;AAE7C;;;GAGG;AACH,MAAM,UAAU,YAAY,CAAC,KAAc,EAAE,GAAW,EAAE,GAAY;IACpE,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,qBAAqB,CAAC,KAAK,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;AACjF,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,YAAY,CAAC,GAAW,EAAE,GAAY,EAAE,iBAAqC;IAC3F,OAAO,UAAU,CACf;QACE,IAAI,EAAE,cAAc;QACpB,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;QACvB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YACnG,cAAc,EAAE,YAAY,CAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,2EAA2E,EACtG,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isByteLengthValidator from 'validator/lib/isByteLength';\n\nexport const IS_BYTE_LENGTH = 'isByteLength';\n\n/**\n * Checks if the string's length (in bytes) falls in a range.\n * If given value is not a string, then it returns false.\n */\nexport function isByteLength(value: unknown, min: number, max?: number): boolean {\n  return typeof value === 'string' && isByteLengthValidator(value, { min, max });\n}\n\n/**\n * Checks if the string's length (in bytes) falls in a range.\n * If given value is not a string, then it returns false.\n */\nexport function IsByteLength(min: number, max?: number, validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_BYTE_LENGTH,\n      constraints: [min, max],\n      validator: {\n        validate: (value, args): boolean => isByteLength(value, args?.constraints[0], args?.constraints[1]),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + \"$property's byte length must fall into ($constraint1, $constraint2) range\",\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}