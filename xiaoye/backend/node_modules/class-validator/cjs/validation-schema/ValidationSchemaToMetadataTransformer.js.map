{"version": 3, "file": "ValidationSchemaToMetadataTransformer.js", "sourceRoot": "", "sources": ["../../../src/validation-schema/ValidationSchemaToMetadataTransformer.ts"], "names": [], "mappings": ";;;AACA,uEAAoE;AAIpE;;GAEG;AACH,MAAa,qCAAqC;IAChD,SAAS,CAAC,MAAwB;QAChC,MAAM,SAAS,GAAyB,EAAE,CAAC;QAC3C,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAChD,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;gBAC/C,MAAM,iBAAiB,GAAsB;oBAC3C,OAAO,EAAE,UAAU,CAAC,OAAO;oBAC3B,MAAM,EAAE,UAAU,CAAC,MAAM;oBACzB,MAAM,EAAE,UAAU,CAAC,MAAM;oBACzB,IAAI,EAAE,UAAU,CAAC,IAAI;iBACtB,CAAC;gBACF,MAAM,IAAI,GAA2B;oBACnC,IAAI,EAAE,UAAU,CAAC,IAAI;oBACrB,IAAI,EAAE,UAAU,CAAC,IAAI;oBACrB,MAAM,EAAE,MAAM,CAAC,IAAI;oBACnB,YAAY,EAAE,QAAQ;oBACtB,WAAW,EAAE,UAAU,CAAC,WAAW;oBACnC,qBAAqB,EAAE,UAAU,CAAC,OAAO;oBACzC,iBAAiB,EAAE,iBAAiB;iBACrC,CAAC;gBACF,SAAS,CAAC,IAAI,CAAC,IAAI,uCAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QACH,OAAO,SAAS,CAAC;IACnB,CAAC;CACF;AAzBD,sFAyBC", "sourcesContent": ["import { ValidationSchema } from './ValidationSchema';\nimport { ValidationMetadata } from '../metadata/ValidationMetadata';\nimport { ValidationMetadataArgs } from '../metadata/ValidationMetadataArgs';\nimport { ValidationOptions } from '../decorator/ValidationOptions';\n\n/**\n * Used to transform validation schemas to validation metadatas.\n */\nexport class ValidationSchemaToMetadataTransformer {\n  transform(schema: ValidationSchema): ValidationMetadata[] {\n    const metadatas: ValidationMetadata[] = [];\n    Object.keys(schema.properties).forEach(property => {\n      schema.properties[property].forEach(validation => {\n        const validationOptions: ValidationOptions = {\n          message: validation.message,\n          groups: validation.groups,\n          always: validation.always,\n          each: validation.each,\n        };\n        const args: ValidationMetadataArgs = {\n          type: validation.type,\n          name: validation.name,\n          target: schema.name,\n          propertyName: property,\n          constraints: validation.constraints,\n          validationTypeOptions: validation.options,\n          validationOptions: validationOptions,\n        };\n        metadatas.push(new ValidationMetadata(args));\n      });\n    });\n    return metadatas;\n  }\n}\n"]}