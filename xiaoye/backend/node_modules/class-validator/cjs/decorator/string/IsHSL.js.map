{"version": 3, "file": "IsHSL.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsHSL.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,gEAAiD;AAEpC,QAAA,MAAM,GAAG,OAAO,CAAC;AAE9B;;;;GAIG;AACH,SAAgB,KAAK,CAAC,KAAc;IAClC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,eAAc,EAAC,KAAK,CAAC,CAAC;AAC5D,CAAC;AAFD,sBAEC;AAED;;;;GAIG;AACH,SAAgB,KAAK,CAAC,iBAAqC;IACzD,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,cAAM;QACZ,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;YAChD,cAAc,EAAE,IAAA,yBAAY,EAAC,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,+BAA+B,EAAE,iBAAiB,CAAC;SAC5G;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAXD,sBAWC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isHSLValidator from 'validator/lib/isHSL';\n\nexport const IS_HSL = 'isHSL';\n\n/**\n * Check if the string is an HSL (hue, saturation, lightness, optional alpha) color based on CSS Colors Level 4 specification.\n * Comma-separated format supported. Space-separated format supported with the exception of a few edge cases (ex: hsl(200grad+.1%62%/1)).\n * If given value is not a string, then it returns false.\n */\nexport function isHSL(value: unknown): boolean {\n  return typeof value === 'string' && isHSLValidator(value);\n}\n\n/**\n * Check if the string is an HSL (hue, saturation, lightness, optional alpha) color based on CSS Colors Level 4 specification.\n * Comma-separated format supported. Space-separated format supported with the exception of a few edge cases (ex: hsl(200grad+.1%62%/1)).\n * If given value is not a string, then it returns false.\n */\nexport function IsHSL(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_HSL,\n      validator: {\n        validate: (value, args): boolean => isHSL(value),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be a HSL color', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}