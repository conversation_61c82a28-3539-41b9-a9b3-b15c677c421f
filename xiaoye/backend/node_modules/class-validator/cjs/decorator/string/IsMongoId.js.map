{"version": 3, "file": "IsMongoId.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsMongoId.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,wEAAyD;AAE5C,QAAA,WAAW,GAAG,WAAW,CAAC;AAEvC;;;GAGG;AACH,SAAgB,SAAS,CAAC,KAAc;IACtC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,mBAAkB,EAAC,KAAK,CAAC,CAAC;AAChE,CAAC;AAFD,8BAEC;AAED;;;GAGG;AACH,SAAgB,SAAS,CAAC,iBAAqC;IAC7D,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,mBAAW;QACjB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC;YACpD,cAAc,EAAE,IAAA,yBAAY,EAAC,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,gCAAgC,EAAE,iBAAiB,CAAC;SAC7G;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAXD,8BAWC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isMongoIdValidator from 'validator/lib/isMongoId';\n\nexport const IS_MONGO_ID = 'isMongoId';\n\n/**\n * Checks if the string is a valid hex-encoded representation of a MongoDB ObjectId.\n * If given value is not a string, then it returns false.\n */\nexport function isMongoId(value: unknown): boolean {\n  return typeof value === 'string' && isMongoIdValidator(value);\n}\n\n/**\n * Checks if the string is a valid hex-encoded representation of a MongoDB ObjectId.\n * If given value is not a string, then it returns false.\n */\nexport function IsMongoId(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_MONGO_ID,\n      validator: {\n        validate: (value, args): boolean => isMongoId(value),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be a mongodb id', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}