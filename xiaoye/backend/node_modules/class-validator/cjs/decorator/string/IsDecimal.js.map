{"version": 3, "file": "IsDecimal.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsDecimal.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,wEAAyD;AAG5C,QAAA,UAAU,GAAG,WAAW,CAAC;AAEtC;;;GAGG;AACH,SAAgB,SAAS,CAAC,KAAc,EAAE,OAAsC;IAC9E,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,mBAAkB,EAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACzE,CAAC;AAFD,8BAEC;AAED;;;GAGG;AACH,SAAgB,SAAS,CACvB,OAAsC,EACtC,iBAAqC;IAErC,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,kBAAU;QAChB,WAAW,EAAE,CAAC,OAAO,CAAC;QACtB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YAC1E,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,0CAA0C,EACrE,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAlBD,8BAkBC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isDecimalValidator from 'validator/lib/isDecimal';\nimport * as ValidatorJS from 'validator';\n\nexport const IS_DECIMAL = 'isDecimal';\n\n/**\n * Checks if the string is a valid decimal.\n * If given value is not a string, then it returns false.\n */\nexport function isDecimal(value: unknown, options?: ValidatorJS.IsDecimalOptions): boolean {\n  return typeof value === 'string' && isDecimalValidator(value, options);\n}\n\n/**\n * Checks if the string is a valid decimal.\n * If given value is not a string, then it returns false.\n */\nexport function IsDecimal(\n  options?: ValidatorJS.IsDecimalOptions,\n  validationOptions?: ValidationOptions\n): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_DECIMAL,\n      constraints: [options],\n      validator: {\n        validate: (value, args): boolean => isDecimal(value, args?.constraints[0]),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property is not a valid decimal number.',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}