{"version": 3, "file": "IsJWT.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsJWT.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,gEAAiD;AAEpC,QAAA,MAAM,GAAG,OAAO,CAAC;AAE9B;;;GAGG;AACH,SAAgB,KAAK,CAAC,KAAc;IAClC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,eAAc,EAAC,KAAK,CAAC,CAAC;AAC5D,CAAC;AAFD,sBAEC;AAED;;;GAGG;AACH,SAAgB,KAAK,CAAC,iBAAqC;IACzD,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,cAAM;QACZ,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;YAChD,cAAc,EAAE,IAAA,yBAAY,EAAC,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,gCAAgC,EAAE,iBAAiB,CAAC;SAC7G;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAXD,sBAWC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isJwtValidator from 'validator/lib/isJWT';\n\nexport const IS_JWT = 'isJwt';\n\n/**\n * Checks if the string is valid JWT token.\n * If given value is not a string, then it returns false.\n */\nexport function isJWT(value: unknown): boolean {\n  return typeof value === 'string' && isJwtValidator(value);\n}\n\n/**\n * Checks if the string is valid JWT token.\n * If given value is not a string, then it returns false.\n */\nexport function IsJWT(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_JWT,\n      validator: {\n        validate: (value, args): boolean => isJWT(value),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be a jwt string', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}