{"version": 3, "file": "NotContains.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/NotContains.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,sEAAuD;AAE1C,QAAA,YAAY,GAAG,aAAa,CAAC;AAE1C;;;GAGG;AACH,SAAgB,WAAW,CAAC,KAAc,EAAE,IAAY;IACtD,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,IAAA,kBAAiB,EAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AACtE,CAAC;AAFD,kCAEC;AAED;;;GAGG;AACH,SAAgB,WAAW,CAAC,IAAY,EAAE,iBAAqC;IAC7E,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,oBAAY;QAClB,WAAW,EAAE,CAAC,IAAI,CAAC;QACnB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YAC5E,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,oDAAoD,EAC/E,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAfD,kCAeC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport containsValidator from 'validator/lib/contains';\n\nexport const NOT_CONTAINS = 'notContains';\n\n/**\n * Checks if the string does not contain the seed.\n * If given value is not a string, then it returns false.\n */\nexport function notContains(value: unknown, seed: string): boolean {\n  return typeof value === 'string' && !containsValidator(value, seed);\n}\n\n/**\n * Checks if the string does not contain the seed.\n * If given value is not a string, then it returns false.\n */\nexport function NotContains(seed: string, validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: NOT_CONTAINS,\n      constraints: [seed],\n      validator: {\n        validate: (value, args): boolean => notContains(value, args?.constraints[0]),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property should not contain a $constraint1 string',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}