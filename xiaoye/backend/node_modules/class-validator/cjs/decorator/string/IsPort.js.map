{"version": 3, "file": "IsPort.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsPort.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,kEAAmD;AAEtC,QAAA,OAAO,GAAG,QAAQ,CAAC;AAEhC;;GAEG;AACH,SAAgB,MAAM,CAAC,KAAc;IACnC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,gBAAe,EAAC,KAAK,CAAC,CAAC;AAC7D,CAAC;AAFD,wBAEC;AAED;;GAEG;AACH,SAAgB,MAAM,CAAC,iBAAqC;IAC1D,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,eAAO;QACb,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC;YACjD,cAAc,EAAE,IAAA,yBAAY,EAAC,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,0BAA0B,EAAE,iBAAiB,CAAC;SACvG;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAXD,wBAWC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isPortValidator from 'validator/lib/isPort';\n\nexport const IS_PORT = 'isPort';\n\n/**\n * Check if the string is a valid port number.\n */\nexport function isPort(value: unknown): boolean {\n  return typeof value === 'string' && isPortValidator(value);\n}\n\n/**\n * Check if the string is a valid port number.\n */\nexport function IsPort(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_PORT,\n      validator: {\n        validate: (value, args): boolean => isPort(value),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be a port', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}