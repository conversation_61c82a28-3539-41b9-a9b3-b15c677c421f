{"version": 3, "file": "IsUppercase.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsUppercase.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,4EAA6D;AAEhD,QAAA,YAAY,GAAG,aAAa,CAAC;AAE1C;;;GAGG;AACH,SAAgB,WAAW,CAAC,KAAc;IACxC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,qBAAoB,EAAC,KAAK,CAAC,CAAC;AAClE,CAAC;AAFD,kCAEC;AAED;;;GAGG;AACH,SAAgB,WAAW,CAAC,iBAAqC;IAC/D,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,oBAAY;QAClB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC;YACtD,cAAc,EAAE,IAAA,yBAAY,EAAC,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,6BAA6B,EAAE,iBAAiB,CAAC;SAC1G;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAXD,kCAWC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isUppercaseValidator from 'validator/lib/isUppercase';\n\nexport const IS_UPPERCASE = 'isUppercase';\n\n/**\n * Checks if the string is uppercase.\n * If given value is not a string, then it returns false.\n */\nexport function isUppercase(value: unknown): boolean {\n  return typeof value === 'string' && isUppercaseValidator(value);\n}\n\n/**\n * Checks if the string is uppercase.\n * If given value is not a string, then it returns false.\n */\nexport function IsUppercase(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_UPPERCASE,\n      validator: {\n        validate: (value, args): boolean => isUppercase(value),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be uppercase', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}