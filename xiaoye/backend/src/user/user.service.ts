import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindManyOptions } from 'typeorm';
import { User, UserRole, UserStatus } from '../database/entities/user.entity';
import * as bcrypt from 'bcrypt';

export interface CreateUserDto {
  email: string;
  password: string;
  name?: string;
  role?: UserRole;
  status?: UserStatus;
}

export interface UpdateUserDto {
  name?: string;
  avatar?: string;
  role?: UserRole;
  status?: UserStatus;
  emailVerified?: boolean;
  preferences?: {
    theme?: 'light' | 'dark';
    language?: 'zh' | 'en';
    defaultBookmarkCategory?: string;
    bookmarkViewMode?: 'grid' | 'list';
  };
}

export interface UserQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  role?: UserRole;
  status?: UserStatus;
  emailVerified?: boolean;
}

export interface UserStatsDto {
  totalUsers: number;
  activeUsers: number;
  newUsersThisMonth: number;
  usersByRole: Record<UserRole, number>;
  usersByStatus: Record<UserStatus, number>;
}

@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  async create(createUserDto: CreateUserDto): Promise<User> {
    const existingUser = await this.userRepository.findOne({
      where: { email: createUserDto.email },
    });

    if (existingUser) {
      throw new ConflictException('用户邮箱已存在');
    }

    const hashedPassword = await bcrypt.hash(createUserDto.password, 10);

    const user = this.userRepository.create({
      ...createUserDto,
      password: hashedPassword,
    });

    return this.userRepository.save(user);
  }

  async findAll(query: UserQueryDto = {}): Promise<{ users: User[]; total: number }> {
    const {
      page = 1,
      limit = 10,
      search,
      role,
      status,
      emailVerified,
    } = query;

    const queryBuilder = this.userRepository.createQueryBuilder('user');

    if (search) {
      queryBuilder.andWhere(
        '(user.email ILIKE :search OR user.name ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    if (role) {
      queryBuilder.andWhere('user.role = :role', { role });
    }

    if (status) {
      queryBuilder.andWhere('user.status = :status', { status });
    }

    if (emailVerified !== undefined) {
      queryBuilder.andWhere('user.emailVerified = :emailVerified', { emailVerified });
    }

    queryBuilder
      .orderBy('user.createdAt', 'DESC')
      .skip((page - 1) * limit)
      .take(limit);

    const [users, total] = await queryBuilder.getManyAndCount();

    return { users, total };
  }

  async findOne(id: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: ['bookmarks', 'categories'],
    });

    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    return user;
  }

  async findByEmail(email: string): Promise<User | null> {
    return this.userRepository.findOne({
      where: { email },
    });
  }

  async update(id: string, updateUserDto: UpdateUserDto): Promise<User> {
    const user = await this.findOne(id);

    Object.assign(user, updateUserDto);

    return this.userRepository.save(user);
  }

  async updatePassword(id: string, newPassword: string): Promise<void> {
    const hashedPassword = await bcrypt.hash(newPassword, 10);
    await this.userRepository.update(id, { password: hashedPassword });
  }

  async updateLastLogin(id: string, ip?: string): Promise<void> {
    await this.userRepository.update(id, {
      lastLoginAt: new Date(),
      lastLoginIp: ip,
    });
  }

  async remove(id: string): Promise<void> {
    const user = await this.findOne(id);
    await this.userRepository.remove(user);
  }

  async getStats(): Promise<UserStatsDto> {
    const totalUsers = await this.userRepository.count();
    
    const activeUsers = await this.userRepository.count({
      where: { status: UserStatus.ACTIVE },
    });

    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);

    const newUsersThisMonth = await this.userRepository
      .createQueryBuilder('user')
      .where('user.createdAt >= :startOfMonth', { startOfMonth })
      .getCount();

    // 按角色统计
    const usersByRole: Record<UserRole, number> = {
      [UserRole.USER]: 0,
      [UserRole.ADMIN]: 0,
    };

    for (const role of Object.values(UserRole)) {
      usersByRole[role] = await this.userRepository.count({
        where: { role },
      });
    }

    // 按状态统计
    const usersByStatus: Record<UserStatus, number> = {
      [UserStatus.ACTIVE]: 0,
      [UserStatus.INACTIVE]: 0,
      [UserStatus.SUSPENDED]: 0,
    };

    for (const status of Object.values(UserStatus)) {
      usersByStatus[status] = await this.userRepository.count({
        where: { status },
      });
    }

    return {
      totalUsers,
      activeUsers,
      newUsersThisMonth,
      usersByRole,
      usersByStatus,
    };
  }

  async setEmailVerified(id: string, verified: boolean): Promise<void> {
    await this.userRepository.update(id, {
      emailVerified: verified,
      emailVerificationToken: verified ? null : undefined,
    });
  }

  async setPasswordResetToken(email: string, token: string, expires: Date): Promise<void> {
    await this.userRepository.update(
      { email },
      {
        passwordResetToken: token,
        passwordResetExpires: expires,
      },
    );
  }

  async clearPasswordResetToken(email: string): Promise<void> {
    await this.userRepository.update(
      { email },
      {
        passwordResetToken: null,
        passwordResetExpires: null,
      },
    );
  }
}
