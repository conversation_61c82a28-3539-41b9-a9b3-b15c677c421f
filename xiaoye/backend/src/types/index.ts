export interface Bookmark {
  id: string;
  title: string;
  url: string;
  description?: string;
  icon?: string;
  favicon?: string;
  sortOrder: number;
  isVisible: boolean;
  isQuickAccess: boolean;
  userId: string;
  categoryId?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Category {
  id: string;
  name: string;
  description?: string;
  icon?: string;
  color?: string;
  sortOrder: number;
  isVisible: boolean;
  isQuickAccess: boolean;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
}
