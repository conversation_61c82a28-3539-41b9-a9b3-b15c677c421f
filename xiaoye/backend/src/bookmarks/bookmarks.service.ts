import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateBookmarkDto, UpdateBookmarkDto } from './dto/bookmark.dto';
import { Bookmark } from '../types';

@Injectable()
export class BookmarksService {
  private bookmarks: Bookmark[] = [
    {
      id: '1',
      title: 'Google',
      url: 'https://www.google.com',
      description: '搜索引擎',
      favicon: 'https://www.google.com/favicon.ico',
      sortOrder: 1,
      isVisible: true,
      isQuickAccess: true,
      userId: 'user1',
      categoryId: 'cat1',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: '2',
      title: 'GitHub',
      url: 'https://github.com',
      description: '代码托管平台',
      favicon: 'https://github.com/favicon.ico',
      sortOrder: 2,
      isVisible: true,
      isQuickAccess: false,
      userId: 'user1',
      categoryId: 'cat2',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];

  async findAll(userId?: string): Promise<Bookmark[]> {
    if (userId) {
      return this.bookmarks.filter(bookmark => bookmark.userId === userId);
    }
    return this.bookmarks;
  }

  async findOne(id: string): Promise<Bookmark> {
    const bookmark = this.bookmarks.find(b => b.id === id);
    if (!bookmark) {
      throw new NotFoundException(`Bookmark with ID ${id} not found`);
    }
    return bookmark;
  }

  async create(userId: string, createBookmarkDto: CreateBookmarkDto): Promise<Bookmark> {
    const newBookmark: Bookmark = {
      id: Date.now().toString(),
      ...createBookmarkDto,
      userId,
      sortOrder: createBookmarkDto.sortOrder ?? 0,
      isVisible: createBookmarkDto.isVisible ?? true,
      isQuickAccess: createBookmarkDto.isQuickAccess ?? false,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    this.bookmarks.push(newBookmark);
    return newBookmark;
  }

  async update(id: string, userId: string, updateBookmarkDto: UpdateBookmarkDto): Promise<Bookmark> {
    const bookmarkIndex = this.bookmarks.findIndex(b => b.id === id && b.userId === userId);
    if (bookmarkIndex === -1) {
      throw new NotFoundException(`Bookmark with ID ${id} not found`);
    }

    this.bookmarks[bookmarkIndex] = {
      ...this.bookmarks[bookmarkIndex],
      ...updateBookmarkDto,
      updatedAt: new Date(),
    };

    return this.bookmarks[bookmarkIndex];
  }

  async remove(id: string, userId: string): Promise<void> {
    const bookmarkIndex = this.bookmarks.findIndex(b => b.id === id && b.userId === userId);
    if (bookmarkIndex === -1) {
      throw new NotFoundException(`Bookmark with ID ${id} not found`);
    }

    this.bookmarks.splice(bookmarkIndex, 1);
  }

  async findByCategory(categoryId: string, userId: string): Promise<Bookmark[]> {
    return this.bookmarks.filter(b => b.categoryId === categoryId && b.userId === userId);
  }

  async findQuickAccess(userId: string): Promise<Bookmark[]> {
    return this.bookmarks.filter(b => b.isQuickAccess && b.userId === userId);
  }
}
