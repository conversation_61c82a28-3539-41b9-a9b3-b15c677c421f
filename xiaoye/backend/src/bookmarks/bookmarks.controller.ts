import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common';
import { BookmarksService } from './bookmarks.service';
import { CreateBookmarkDto, UpdateBookmarkDto } from './dto/bookmark.dto';
import { Bookmark } from '../types';

@Controller('bookmarks')
export class BookmarksController {
  constructor(private readonly bookmarksService: BookmarksService) {}

  @Post()
  create(
    @Body() createBookmarkDto: CreateBookmarkDto,
    @Query('userId') userId: string = 'user1', // 临时硬编码，等认证系统启用后使用真实用户ID
  ): Promise<Bookmark> {
    return this.bookmarksService.create(userId, createBookmarkDto);
  }

  @Get()
  findAll(@Query('userId') userId?: string): Promise<Bookmark[]> {
    return this.bookmarksService.findAll(userId);
  }

  @Get('quick-access')
  findQuickAccess(@Query('userId') userId: string = 'user1'): Promise<Bookmark[]> {
    return this.bookmarksService.findQuickAccess(userId);
  }

  @Get('category/:categoryId')
  findByCategory(
    @Param('categoryId') categoryId: string,
    @Query('userId') userId: string = 'user1',
  ): Promise<Bookmark[]> {
    return this.bookmarksService.findByCategory(categoryId, userId);
  }

  @Get(':id')
  findOne(@Param('id') id: string): Promise<Bookmark> {
    return this.bookmarksService.findOne(id);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateBookmarkDto: UpdateBookmarkDto,
    @Query('userId') userId: string = 'user1',
  ): Promise<Bookmark> {
    return this.bookmarksService.update(id, userId, updateBookmarkDto);
  }

  @Delete(':id')
  remove(
    @Param('id') id: string,
    @Query('userId') userId: string = 'user1',
  ): Promise<void> {
    return this.bookmarksService.remove(id, userId);
  }
}
