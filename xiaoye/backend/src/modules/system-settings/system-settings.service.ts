import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SystemSetting, SettingType } from '../../database/entities/system-setting.entity';

export interface CreateSettingDto {
  key: string;
  value: any;
  type: SettingType;
  description?: string;
  isPublic?: boolean;
  isEditable?: boolean;
}

export interface UpdateSettingDto {
  value?: any;
  description?: string;
  isPublic?: boolean;
  isEditable?: boolean;
}

@Injectable()
export class SystemSettingsService {
  constructor(
    @InjectRepository(SystemSetting)
    private readonly settingRepository: Repository<SystemSetting>,
  ) {}

  async onModuleInit() {
    // 初始化默认设置
    await this.initializeDefaultSettings();
  }

  private async initializeDefaultSettings() {
    const defaultSettings = [
      {
        key: 'site.name',
        value: '小野导航',
        type: SettingType.STRING,
        description: '网站名称',
        isPublic: true,
        isEditable: true,
      },
      {
        key: 'site.description',
        value: '个人书签管理工具',
        type: SettingType.STRING,
        description: '网站描述',
        isPublic: true,
        isEditable: true,
      },
      {
        key: 'auth.allowRegistration',
        value: true,
        type: SettingType.BOOLEAN,
        description: '是否允许用户注册',
        isPublic: true,
        isEditable: true,
      },
      {
        key: 'auth.requireEmailVerification',
        value: false,
        type: SettingType.BOOLEAN,
        description: '是否需要邮箱验证',
        isPublic: false,
        isEditable: true,
      },
      {
        key: 'auth.maxLoginAttempts',
        value: 5,
        type: SettingType.NUMBER,
        description: '最大登录尝试次数',
        isPublic: false,
        isEditable: true,
      },
      {
        key: 'bookmarks.maxPerUser',
        value: 1000,
        type: SettingType.NUMBER,
        description: '每个用户最大书签数量',
        isPublic: false,
        isEditable: true,
      },
      {
        key: 'bookmarks.defaultCategories',
        value: ['新闻资讯', '视频网站', '游戏直播', '社交媒体', '购物网站', '搜索引擎', '工具网站', '学习教育', '音乐网站'],
        type: SettingType.JSON,
        description: '默认书签分类',
        isPublic: true,
        isEditable: true,
      },
      {
        key: 'ui.defaultTheme',
        value: 'light',
        type: SettingType.STRING,
        description: '默认主题',
        isPublic: true,
        isEditable: true,
      },
      {
        key: 'ui.defaultLanguage',
        value: 'zh',
        type: SettingType.STRING,
        description: '默认语言',
        isPublic: true,
        isEditable: true,
      },
    ];

    for (const setting of defaultSettings) {
      const exists = await this.settingRepository.findOne({
        where: { key: setting.key },
      });

      if (!exists) {
        const newSetting = this.settingRepository.create({
          key: setting.key,
          type: setting.type,
          description: setting.description,
          isPublic: setting.isPublic,
          isEditable: setting.isEditable,
        });
        newSetting.setValue(setting.value);
        await this.settingRepository.save(newSetting);
      }
    }
  }

  async findAll(): Promise<SystemSetting[]> {
    return this.settingRepository.find({
      order: { key: 'ASC' },
    });
  }

  async findPublic(): Promise<Record<string, any>> {
    const settings = await this.settingRepository.find({
      where: { isPublic: true },
    });

    const result: Record<string, any> = {};
    settings.forEach(setting => {
      result[setting.key] = setting.getParsedValue();
    });

    return result;
  }

  async findByKey(key: string): Promise<SystemSetting> {
    const setting = await this.settingRepository.findOne({
      where: { key },
    });

    if (!setting) {
      throw new NotFoundException(`Setting with key "${key}" not found`);
    }

    return setting;
  }

  async getValue(key: string): Promise<any> {
    const setting = await this.findByKey(key);
    return setting.getParsedValue();
  }

  async create(createDto: CreateSettingDto): Promise<SystemSetting> {
    const setting = this.settingRepository.create({
      key: createDto.key,
      type: createDto.type,
      description: createDto.description,
      isPublic: createDto.isPublic,
      isEditable: createDto.isEditable,
    });
    setting.setValue(createDto.value);
    return this.settingRepository.save(setting);
  }

  async update(key: string, updateDto: UpdateSettingDto): Promise<SystemSetting> {
    const setting = await this.findByKey(key);

    if (updateDto.value !== undefined) {
      setting.setValue(updateDto.value);
    }

    if (updateDto.description !== undefined) {
      setting.description = updateDto.description;
    }

    if (updateDto.isPublic !== undefined) {
      setting.isPublic = updateDto.isPublic;
    }

    if (updateDto.isEditable !== undefined) {
      setting.isEditable = updateDto.isEditable;
    }

    return this.settingRepository.save(setting);
  }

  async delete(key: string): Promise<void> {
    const setting = await this.findByKey(key);
    await this.settingRepository.remove(setting);
  }

  // 批量更新设置
  async updateMultiple(updates: Record<string, any>): Promise<void> {
    for (const [key, value] of Object.entries(updates)) {
      try {
        await this.update(key, { value });
      } catch (error) {
        // 如果设置不存在，跳过
        console.warn(`Failed to update setting ${key}:`, error.message);
      }
    }
  }
}
