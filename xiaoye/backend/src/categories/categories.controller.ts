import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common';
import { CategoriesService } from './categories.service';
import { CreateCategoryDto, UpdateCategoryDto } from './dto/category.dto';
import { Category } from '../types';

@Controller('categories')
export class CategoriesController {
  constructor(private readonly categoriesService: CategoriesService) {}

  @Post()
  create(
    @Body() createCategoryDto: CreateCategoryDto,
    @Query('userId') userId: string = 'user1',
  ): Promise<Category> {
    return this.categoriesService.create(userId, createCategoryDto);
  }

  @Get()
  findAll(@Query('userId') userId?: string): Promise<Category[]> {
    return this.categoriesService.findAll(userId);
  }

  @Get('quick-access')
  findQuickAccess(@Query('userId') userId: string = 'user1'): Promise<Category[]> {
    return this.categoriesService.findQuickAccess(userId);
  }

  @Get(':id')
  findOne(@Param('id') id: string): Promise<Category> {
    return this.categoriesService.findOne(id);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateCategoryDto: UpdateCategoryDto,
    @Query('userId') userId: string = 'user1',
  ): Promise<Category> {
    return this.categoriesService.update(id, userId, updateCategoryDto);
  }

  @Delete(':id')
  remove(
    @Param('id') id: string,
    @Query('userId') userId: string = 'user1',
  ): Promise<void> {
    return this.categoriesService.remove(id, userId);
  }
}
