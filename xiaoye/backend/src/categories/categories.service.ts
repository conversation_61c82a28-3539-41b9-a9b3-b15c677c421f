import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateCategoryDto, UpdateCategoryDto } from './dto/category.dto';
import { Category } from '../types';

@Injectable()
export class CategoriesService {
  private categories: Category[] = [
    {
      id: 'cat1',
      name: '搜索引擎',
      description: '各种搜索引擎网站',
      icon: 'search',
      color: '#1976d2',
      sortOrder: 1,
      isVisible: true,
      isQuickAccess: true,
      userId: 'user1',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: 'cat2',
      name: '开发工具',
      description: '编程开发相关工具',
      icon: 'code',
      color: '#2e7d32',
      sortOrder: 2,
      isVisible: true,
      isQuickAccess: false,
      userId: 'user1',
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];

  async findAll(userId?: string): Promise<Category[]> {
    if (userId) {
      return this.categories.filter(category => category.userId === userId);
    }
    return this.categories;
  }

  async findOne(id: string): Promise<Category> {
    const category = this.categories.find(c => c.id === id);
    if (!category) {
      throw new NotFoundException(`Category with ID ${id} not found`);
    }
    return category;
  }

  async create(userId: string, createCategoryDto: CreateCategoryDto): Promise<Category> {
    const newCategory: Category = {
      id: Date.now().toString(),
      ...createCategoryDto,
      userId,
      sortOrder: createCategoryDto.sortOrder ?? 0,
      isVisible: createCategoryDto.isVisible ?? true,
      isQuickAccess: createCategoryDto.isQuickAccess ?? false,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    this.categories.push(newCategory);
    return newCategory;
  }

  async update(id: string, userId: string, updateCategoryDto: UpdateCategoryDto): Promise<Category> {
    const categoryIndex = this.categories.findIndex(c => c.id === id && c.userId === userId);
    if (categoryIndex === -1) {
      throw new NotFoundException(`Category with ID ${id} not found`);
    }

    this.categories[categoryIndex] = {
      ...this.categories[categoryIndex],
      ...updateCategoryDto,
      updatedAt: new Date(),
    };

    return this.categories[categoryIndex];
  }

  async remove(id: string, userId: string): Promise<void> {
    const categoryIndex = this.categories.findIndex(c => c.id === id && c.userId === userId);
    if (categoryIndex === -1) {
      throw new NotFoundException(`Category with ID ${id} not found`);
    }

    this.categories.splice(categoryIndex, 1);
  }

  async findQuickAccess(userId: string): Promise<Category[]> {
    return this.categories.filter(c => c.isQuickAccess && c.userId === userId);
  }
}
