import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { User } from './user.entity';
import { Category } from './category.entity';

@Entity('bookmarks')
export class Bookmark {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  title: string;

  @Column()
  url: string;

  @Column({ nullable: true })
  description: string;

  @Column({ nullable: true })
  icon: string;

  @Column({ nullable: true })
  favicon: string;

  @Column({ default: 0 })
  sortOrder: number;

  @Column({ default: true })
  isVisible: boolean;

  @Column({ default: false })
  isQuickAccess: boolean;

  @Column({ default: 0 })
  clickCount: number; // 点击次数

  @Column({ nullable: true })
  lastAccessedAt: Date; // 最后访问时间

  @Column({ type: 'json', nullable: true })
  tags: string[]; // 标签

  @Column({ type: 'json', nullable: true })
  metadata: {
    domain?: string;
    screenshot?: string;
    pageTitle?: string;
    pageDescription?: string;
  };

  @Column('uuid')
  userId: string;

  @Column('uuid', { nullable: true })
  categoryId: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(() => User, (user) => user.bookmarks, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'userId' })
  user: User;

  @ManyToOne(() => Category, (category) => category.bookmarks, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'categoryId' })
  category: Category;
}
