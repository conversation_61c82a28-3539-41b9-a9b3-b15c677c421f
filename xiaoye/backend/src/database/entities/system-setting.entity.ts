import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

export enum SettingType {
  BOOLEAN = 'boolean',
  STRING = 'string',
  NUMBER = 'number',
  JSON = 'json'
}

@Entity('system_settings')
export class SystemSetting {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  key: string;

  @Column({ type: 'text' })
  value: string;

  @Column({
    type: 'enum',
    enum: SettingType,
    default: SettingType.STRING
  })
  type: SettingType;

  @Column({ nullable: true })
  description: string;

  @Column({ default: false })
  isPublic: boolean; // 是否可以被前端访问

  @Column({ default: true })
  isEditable: boolean; // 是否可以在管理后台编辑

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // 获取解析后的值
  getParsedValue(): any {
    switch (this.type) {
      case SettingType.BOOLEAN:
        return this.value === 'true';
      case SettingType.NUMBER:
        return parseFloat(this.value);
      case SettingType.JSON:
        try {
          return JSON.parse(this.value);
        } catch {
          return null;
        }
      default:
        return this.value;
    }
  }

  // 设置值
  setValue(value: any): void {
    switch (this.type) {
      case SettingType.BOOLEAN:
        this.value = value ? 'true' : 'false';
        break;
      case SettingType.NUMBER:
        this.value = value.toString();
        break;
      case SettingType.JSON:
        this.value = JSON.stringify(value);
        break;
      default:
        this.value = value.toString();
    }
  }
}
