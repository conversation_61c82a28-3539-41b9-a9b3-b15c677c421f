import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SystemSetting, SettingType } from '../entities/system-setting.entity';

@Injectable()
export class SystemSettingsSeed {
  constructor(
    @InjectRepository(SystemSetting)
    private readonly settingRepository: Repository<SystemSetting>,
  ) {}

  async run(): Promise<void> {
    const defaultSettings = [
      {
        key: 'site.name',
        value: '小野导航',
        type: SettingType.STRING,
        description: '网站名称',
        isPublic: true,
        isEditable: true,
      },
      {
        key: 'site.description',
        value: '个人书签管理工具',
        type: SettingType.STRING,
        description: '网站描述',
        isPublic: true,
        isEditable: true,
      },
      {
        key: 'site.version',
        value: '1.0.0',
        type: SettingType.STRING,
        description: '系统版本',
        isPublic: true,
        isEditable: false,
      },
      {
        key: 'auth.allowRegistration',
        value: true,
        type: SettingType.BOOLEAN,
        description: '是否允许用户注册',
        isPublic: true,
        isEditable: true,
      },
      {
        key: 'auth.requireEmailVerification',
        value: false,
        type: SettingType.BOOLEAN,
        description: '是否需要邮箱验证',
        isPublic: false,
        isEditable: true,
      },
      {
        key: 'auth.maxLoginAttempts',
        value: 5,
        type: SettingType.NUMBER,
        description: '最大登录尝试次数',
        isPublic: false,
        isEditable: true,
      },
      {
        key: 'auth.sessionTimeout',
        value: 24,
        type: SettingType.NUMBER,
        description: '会话超时时间（小时）',
        isPublic: false,
        isEditable: true,
      },
      {
        key: 'bookmarks.maxPerUser',
        value: 1000,
        type: SettingType.NUMBER,
        description: '每个用户最大书签数量',
        isPublic: false,
        isEditable: true,
      },
      {
        key: 'bookmarks.defaultCategories',
        value: [
          '新闻资讯',
          '视频网站',
          '游戏直播',
          '社交媒体',
          '购物网站',
          '搜索引擎',
          '工具网站',
          '学习教育',
          '音乐网站',
          '设计素材',
          '开发工具',
          '云服务'
        ],
        type: SettingType.JSON,
        description: '默认书签分类',
        isPublic: true,
        isEditable: true,
      },
      {
        key: 'ui.defaultTheme',
        value: 'light',
        type: SettingType.STRING,
        description: '默认主题',
        isPublic: true,
        isEditable: true,
      },
      {
        key: 'ui.defaultLanguage',
        value: 'zh',
        type: SettingType.STRING,
        description: '默认语言',
        isPublic: true,
        isEditable: true,
      },
      {
        key: 'ui.enableDarkMode',
        value: true,
        type: SettingType.BOOLEAN,
        description: '是否启用暗色模式',
        isPublic: true,
        isEditable: true,
      },
      {
        key: 'security.enableRateLimit',
        value: true,
        type: SettingType.BOOLEAN,
        description: '是否启用API限流',
        isPublic: false,
        isEditable: true,
      },
      {
        key: 'security.rateLimitWindow',
        value: 15,
        type: SettingType.NUMBER,
        description: '限流时间窗口（分钟）',
        isPublic: false,
        isEditable: true,
      },
      {
        key: 'security.rateLimitMax',
        value: 100,
        type: SettingType.NUMBER,
        description: '限流最大请求数',
        isPublic: false,
        isEditable: true,
      },
      {
        key: 'backup.autoBackup',
        value: false,
        type: SettingType.BOOLEAN,
        description: '是否启用自动备份',
        isPublic: false,
        isEditable: true,
      },
      {
        key: 'backup.backupInterval',
        value: 24,
        type: SettingType.NUMBER,
        description: '备份间隔（小时）',
        isPublic: false,
        isEditable: true,
      },
      {
        key: 'email.smtpHost',
        value: '',
        type: SettingType.STRING,
        description: 'SMTP服务器地址',
        isPublic: false,
        isEditable: true,
      },
      {
        key: 'email.smtpPort',
        value: 587,
        type: SettingType.NUMBER,
        description: 'SMTP端口',
        isPublic: false,
        isEditable: true,
      },
      {
        key: 'email.smtpUser',
        value: '',
        type: SettingType.STRING,
        description: 'SMTP用户名',
        isPublic: false,
        isEditable: true,
      },
      {
        key: 'email.fromAddress',
        value: '<EMAIL>',
        type: SettingType.STRING,
        description: '发件人邮箱',
        isPublic: false,
        isEditable: true,
      },
    ];

    for (const settingData of defaultSettings) {
      const exists = await this.settingRepository.findOne({
        where: { key: settingData.key },
      });

      if (!exists) {
        const setting = this.settingRepository.create({
          key: settingData.key,
          type: settingData.type,
          description: settingData.description,
          isPublic: settingData.isPublic,
          isEditable: settingData.isEditable,
        });
        setting.setValue(settingData.value);
        await this.settingRepository.save(setting);
        console.log(`✓ 创建系统设置: ${settingData.key}`);
      }
    }

    console.log('✓ 系统设置初始化完成');
  }
}
