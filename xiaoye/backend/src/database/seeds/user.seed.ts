import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcrypt';
import { User, UserRole } from '../entities/user.entity';

@Injectable()
export class UserSeedService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  async seed() {
    // 检查是否已存在管理员账号
    const existingAdmin = await this.userRepository.findOne({
      where: { email: '<EMAIL>' }
    });

    if (!existingAdmin) {
      // 创建管理员账号
      const hashedPassword = await bcrypt.hash('admin123456', 10);
      const admin = this.userRepository.create({
        email: '<EMAIL>',
        password: hashedPassword,
        name: '系统管理员',
        role: UserRole.ADMIN,
        isActive: true,
      });
      await this.userRepository.save(admin);
      console.log('✅ 管理员账号创建成功');
      console.log('📧 邮箱: <EMAIL>');
      console.log('🔑 密码: admin123456');
    }

    // 检查是否已存在测试用户
    const existingUser = await this.userRepository.findOne({
      where: { email: '<EMAIL>' }
    });

    if (!existingUser) {
      // 创建测试用户
      const hashedPassword = await bcrypt.hash('user123456', 10);
      const user = this.userRepository.create({
        email: '<EMAIL>',
        password: hashedPassword,
        name: '测试用户',
        role: UserRole.USER,
        isActive: true,
      });
      await this.userRepository.save(user);
      console.log('✅ 测试用户创建成功');
      console.log('📧 邮箱: <EMAIL>');
      console.log('🔑 密码: user123456');
    }
  }
}
