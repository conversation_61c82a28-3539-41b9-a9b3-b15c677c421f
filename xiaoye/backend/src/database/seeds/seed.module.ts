import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '../entities/user.entity';
import { Category } from '../entities/category.entity';
import { Bookmark } from '../entities/bookmark.entity';
import { UserSeedService } from './user.seed';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Category, Bookmark])
  ],
  providers: [UserSeedService],
  exports: [UserSeedService],
})
export class SeedModule {}
