import { MigrationInterface, QueryRunner, Table, TableColumn } from 'typeorm';

export class EnhanceUserSystem1703000001 implements MigrationInterface {
  name = 'EnhanceUserSystem1703000001';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 添加用户表的新字段
    await queryRunner.addColumns('users', [
      new TableColumn({
        name: 'status',
        type: 'enum',
        enum: ['active', 'inactive', 'suspended'],
        default: "'active'",
        isNullable: false,
      }),
      new TableColumn({
        name: 'emailVerified',
        type: 'boolean',
        default: false,
        isNullable: false,
      }),
      new TableColumn({
        name: 'emailVerificationToken',
        type: 'varchar',
        length: '255',
        isNullable: true,
      }),
      new TableColumn({
        name: 'passwordResetToken',
        type: 'varchar',
        length: '255',
        isNullable: true,
      }),
      new TableColumn({
        name: 'passwordResetExpires',
        type: 'timestamp',
        isNullable: true,
      }),
      new TableColumn({
        name: 'lastLoginAt',
        type: 'timestamp',
        isNullable: true,
      }),
      new TableColumn({
        name: 'lastLoginIp',
        type: 'varchar',
        length: '45',
        isNullable: true,
      }),
      new TableColumn({
        name: 'preferences',
        type: 'json',
        isNullable: true,
      }),
    ]);

    // 添加书签表的新字段
    await queryRunner.addColumns('bookmarks', [
      new TableColumn({
        name: 'clickCount',
        type: 'int',
        default: 0,
        isNullable: false,
      }),
      new TableColumn({
        name: 'lastAccessedAt',
        type: 'timestamp',
        isNullable: true,
      }),
      new TableColumn({
        name: 'tags',
        type: 'json',
        isNullable: true,
      }),
      new TableColumn({
        name: 'metadata',
        type: 'json',
        isNullable: true,
      }),
    ]);

    // 创建系统设置表
    await queryRunner.createTable(
      new Table({
        name: 'system_settings',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()',
          },
          {
            name: 'key',
            type: 'varchar',
            length: '255',
            isUnique: true,
            isNullable: false,
          },
          {
            name: 'value',
            type: 'text',
            isNullable: false,
          },
          {
            name: 'type',
            type: 'enum',
            enum: ['boolean', 'string', 'number', 'json'],
            default: "'string'",
            isNullable: false,
          },
          {
            name: 'description',
            type: 'varchar',
            length: '500',
            isNullable: true,
          },
          {
            name: 'isPublic',
            type: 'boolean',
            default: false,
            isNullable: false,
          },
          {
            name: 'isEditable',
            type: 'boolean',
            default: true,
            isNullable: false,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            isNullable: false,
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
            isNullable: false,
          },
        ],
      }),
      true,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // 删除系统设置表
    await queryRunner.dropTable('system_settings');

    // 删除书签表的新字段
    await queryRunner.dropColumns('bookmarks', [
      'clickCount',
      'lastAccessedAt',
      'tags',
      'metadata',
    ]);

    // 删除用户表的新字段
    await queryRunner.dropColumns('users', [
      'status',
      'emailVerified',
      'emailVerificationToken',
      'passwordResetToken',
      'passwordResetExpires',
      'lastLoginAt',
      'lastLoginIp',
      'preferences',
    ]);
  }
}
