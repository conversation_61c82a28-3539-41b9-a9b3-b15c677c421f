{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "extract": "lingui extract", "compile": "lingui compile"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@lingui/core": "^5.3.3", "@lingui/macro": "^5.3.3", "@lingui/react": "^5.3.3", "@mui/icons-material": "^7.2.0", "@mui/lab": "^7.0.0-beta.14", "@mui/material": "^7.2.0", "framer-motion": "^12.23.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.3", "recoil": "^0.7.7"}, "devDependencies": {"@eslint/js": "^9.30.1", "@lingui/cli": "^5.3.3", "@lingui/vite-plugin": "^5.3.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}