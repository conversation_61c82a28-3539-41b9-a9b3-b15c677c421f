import { atom } from 'recoil';

export type SupportedLocale = 'en' | 'zh';

// 语言设置状态
export const localeState = atom<SupportedLocale>({
  key: 'localeState',
  default: 'en',
});

// 从localStorage恢复语言设置
export const getInitialLocale = (): SupportedLocale => {
  if (typeof window !== 'undefined') {
    const saved = localStorage.getItem('locale');
    if (saved === 'en' || saved === 'zh') {
      return saved;
    }
    // 检查浏览器语言偏好
    const browserLang = navigator.language.toLowerCase();
    if (browserLang.startsWith('zh')) {
      return 'zh';
    }
  }
  return 'en';
};
