import { atom, selector } from 'recoil';
import { Bookmark, Category } from '../../services/api';

// 书签状态
export const bookmarksState = atom<Bookmark[]>({
  key: 'bookmarksState',
  default: [],
});

// 分类状态
export const categoriesState = atom<Category[]>({
  key: 'categoriesState',
  default: [],
});

// 加载状态
export const bookmarksLoadingState = atom<boolean>({
  key: 'bookmarksLoadingState',
  default: false,
});

export const categoriesLoadingState = atom<boolean>({
  key: 'categoriesLoadingState',
  default: false,
});

// 选择器：快捷访问书签
export const quickAccessBookmarksSelector = selector({
  key: 'quickAccessBookmarksSelector',
  get: ({ get }) => {
    const bookmarks = get(bookmarksState);
    return bookmarks.filter(bookmark => bookmark.isQuickAccess && bookmark.isVisible);
  },
});

// 选择器：快捷访问分类
export const quickAccessCategoriesSelector = selector({
  key: 'quickAccessCategoriesSelector',
  get: ({ get }) => {
    const categories = get(categoriesState);
    return categories.filter(category => category.isQuickAccess && category.isVisible);
  },
});

// 选择器：按分类分组的书签
export const bookmarksByCategorySelector = selector({
  key: 'bookmarksByCategorySelector',
  get: ({ get }) => {
    const bookmarks = get(bookmarksState);
    const categories = get(categoriesState);
    
    const grouped: Record<string, { category: Category; bookmarks: Bookmark[] }> = {};
    
    // 初始化分类
    categories.forEach(category => {
      if (category.isVisible) {
        grouped[category.id] = {
          category,
          bookmarks: [],
        };
      }
    });
    
    // 分组书签
    bookmarks.forEach(bookmark => {
      if (bookmark.isVisible && bookmark.categoryId && grouped[bookmark.categoryId]) {
        grouped[bookmark.categoryId].bookmarks.push(bookmark);
      }
    });
    
    // 按排序顺序排序
    Object.values(grouped).forEach(group => {
      group.bookmarks.sort((a, b) => a.sortOrder - b.sortOrder);
    });
    
    return grouped;
  },
});
