import { atom } from 'recoil';

// 用户信息类型
export interface User {
  id: string;
  email: string;
  name?: string;
  avatar?: string;
  createdAt: string;
  updatedAt: string;
}

// 认证状态
export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  refreshToken: string | null;
}

// 认证状态原子
export const authState = atom<AuthState>({
  key: 'authState',
  default: {
    isAuthenticated: false,
    user: null,
    token: null,
    refreshToken: null,
  },
});

// 加载状态
export const authLoadingState = atom<boolean>({
  key: 'authLoadingState',
  default: false,
});
