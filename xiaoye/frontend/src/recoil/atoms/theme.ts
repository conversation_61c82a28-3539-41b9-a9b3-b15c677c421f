import { atom } from 'recoil';

// 主题模式类型
export type ThemeMode = 'light' | 'dark';

// 主题模式状态
export const themeModeState = atom<ThemeMode>({
  key: 'themeModeState',
  default: 'light',
});

// 从localStorage恢复主题设置
export const getInitialThemeMode = (): ThemeMode => {
  if (typeof window !== 'undefined') {
    const saved = localStorage.getItem('themeMode');
    if (saved === 'light' || saved === 'dark') {
      return saved;
    }
    // 检查系统偏好
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      return 'dark';
    }
  }
  return 'light';
};
