import React from 'react';
import {
  AppBar,
  Toolbar,
  Typo<PERSON>,
  IconButton,
  Box,
  Container,
} from '@mui/material';
import {
  Brightness4,
  Brightness7,
  Language,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import { useThemeStore } from '../stores/theme';
import { useLocaleStore } from '../stores/locale';

interface MainLayoutProps {
  children: React.ReactNode;
}

export const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const { mode: themeMode, toggleMode } = useThemeStore();
  const { locale, setLocale } = useLocaleStore();

  const handleThemeToggle = () => {
    toggleMode();
  };

  const handleLocaleToggle = () => {
    const newLocale = locale === 'en' ? 'zh' : 'en';
    setLocale(newLocale);
  };

  return (
    <Box sx={{
      minHeight: '100vh',
      display: 'flex',
      flexDirection: 'column',
      bgcolor: 'background.default',
      background: themeMode === 'dark'
        ? 'linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)'
        : 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)'
    }}>
      <AppBar
        position="static"
        elevation={0}
        sx={{
          background: themeMode === 'dark'
            ? 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)'
            : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          backdropFilter: 'blur(10px)',
          borderBottom: '1px solid',
          borderColor: 'divider'
        }}
      >
        <Container maxWidth="xl">
          <Toolbar sx={{ px: { xs: 1, sm: 2 }, py: 1 }}>
            <Box display="flex" alignItems="center" sx={{ flexGrow: 1 }}>
              <Box
                sx={{
                  width: 40,
                  height: 40,
                  borderRadius: 2,
                  background: 'rgba(255, 255, 255, 0.2)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  mr: 2,
                  fontSize: '1.5rem'
                }}
              >
                🚀
              </Box>
              <Typography
                variant="h5"
                component="div"
                sx={{
                  fontWeight: 700,
                  fontSize: { xs: '1.25rem', sm: '1.5rem' },
                  background: 'linear-gradient(45deg, #fff 30%, rgba(255,255,255,0.8) 90%)',
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  textShadow: '0 2px 4px rgba(0,0,0,0.1)'
                }}
              >
                小野在线导航
              </Typography>
            </Box>

            <Box display="flex" alignItems="center" gap={1}>
              <IconButton
                color="inherit"
                onClick={handleLocaleToggle}
                size="large"
                sx={{
                  bgcolor: 'rgba(255, 255, 255, 0.1)',
                  '&:hover': { bgcolor: 'rgba(255, 255, 255, 0.2)' }
                }}
              >
                <Language />
              </IconButton>

              <IconButton
                color="inherit"
                onClick={handleThemeToggle}
                size="large"
                sx={{
                  bgcolor: 'rgba(255, 255, 255, 0.1)',
                  '&:hover': { bgcolor: 'rgba(255, 255, 255, 0.2)' }
                }}
              >
                {themeMode === 'dark' ? <Brightness7 /> : <Brightness4 />}
              </IconButton>

              <IconButton
                color="inherit"
                size="large"
                sx={{
                  bgcolor: 'rgba(255, 255, 255, 0.1)',
                  '&:hover': { bgcolor: 'rgba(255, 255, 255, 0.2)' }
                }}
              >
                <SettingsIcon />
              </IconButton>
            </Box>
          </Toolbar>
        </Container>
      </AppBar>

      <Container
        maxWidth="xl"
        sx={{
          flex: 1,
          py: { xs: 3, sm: 4, md: 6 },
          px: { xs: 2, sm: 3, md: 4 },
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'flex-start',
          minHeight: 'calc(100vh - 80px)'
        }}
      >
        <Box
          sx={{
            width: '100%',
            maxWidth: '1400px',
            mx: 'auto',
            background: themeMode === 'dark'
              ? 'rgba(255, 255, 255, 0.05)'
              : 'rgba(255, 255, 255, 0.8)',
            backdropFilter: 'blur(20px)',
            borderRadius: 4,
            p: { xs: 3, sm: 4, md: 6 },
            boxShadow: themeMode === 'dark'
              ? '0 8px 32px rgba(0, 0, 0, 0.3)'
              : '0 8px 32px rgba(0, 0, 0, 0.1)',
            border: '1px solid',
            borderColor: themeMode === 'dark'
              ? 'rgba(255, 255, 255, 0.1)'
              : 'rgba(255, 255, 255, 0.3)'
          }}
        >
          {children}
        </Box>
      </Container>
    </Box>
  );
};
