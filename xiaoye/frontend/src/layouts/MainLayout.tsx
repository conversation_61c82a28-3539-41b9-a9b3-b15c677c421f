import React, { useState } from 'react';
import {
  Box,
  IconButton,
  Typography,
  Button,
  Avatar,
  Menu,
  MenuItem,
  Divider,
  Chip
} from '@mui/material';
import {
  Brightness4,
  Brightness7,
  Language,
  Login,
  Logout,
  Settings,
  CloudSync
} from '@mui/icons-material';
import { useThemeStore } from '../stores/theme';
import { useLocaleStore } from '../stores/locale';
import { useAuthStore } from '../stores/auth';
import { LoginDialog } from '../components/LoginDialog';
import { useTranslation } from '../hooks/useTranslation';

interface MainLayoutProps {
  children: React.ReactNode;
}

export const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const { mode: themeMode, toggleMode } = useThemeStore();
  const { locale, setLocale } = useLocaleStore();
  const { user, isAuthenticated, logout } = useAuthStore();
  const { t } = useTranslation();
  const [loginDialogOpen, setLoginDialogOpen] = useState(false);
  const [userMenuAnchor, setUserMenuAnchor] = useState<null | HTMLElement>(null);

  const handleThemeToggle = () => {
    toggleMode();
  };

  const handleLocaleToggle = () => {
    const newLocale = locale === 'en' ? 'zh' : 'en';
    setLocale(newLocale);
  };

  const handleUserMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setUserMenuAnchor(event.currentTarget);
  };

  const handleUserMenuClose = () => {
    setUserMenuAnchor(null);
  };

  const handleLogout = () => {
    logout();
    handleUserMenuClose();
  };

  return (
    <>
      {/* 固定页眉 */}
      <Box
        sx={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          zIndex: 1000,
          bgcolor: themeMode === 'dark' ? '#1e1e1e' : '#fff',
          borderBottom: `1px solid ${themeMode === 'dark' ? '#333' : '#e0e0e0'}`,
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            p: 2,
            maxWidth: '1200px',
            mx: 'auto',
          }}
        >
          {/* 左侧标题 */}
          <Box>
            <Typography
              variant="h5"
              sx={{
                fontWeight: 600,
                color: themeMode === 'dark' ? '#fff' : '#333',
              }}
            >
              {t.nav.title}
            </Typography>
          </Box>

          {/* 右侧工具栏 */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {/* 用户状态显示 */}
            {isAuthenticated && (
              <Chip
                icon={<CloudSync />}
                label={t.nav.syncStatus}
                size="small"
                color="success"
                sx={{
                  bgcolor: themeMode === 'dark' ? 'rgba(76, 175, 80, 0.2)' : 'rgba(76, 175, 80, 0.1)',
                  color: themeMode === 'dark' ? '#81c784' : '#4caf50',
                  mr: 1,
                }}
              />
            )}

            <IconButton
              onClick={handleLocaleToggle}
              sx={{
                bgcolor: themeMode === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',
                '&:hover': {
                  bgcolor: themeMode === 'dark' ? 'rgba(255,255,255,0.2)' : 'rgba(0,0,0,0.2)',
                },
              }}
            >
              <Language />
            </IconButton>

            <IconButton
              onClick={handleThemeToggle}
              sx={{
                bgcolor: themeMode === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',
                '&:hover': {
                  bgcolor: themeMode === 'dark' ? 'rgba(255,255,255,0.2)' : 'rgba(0,0,0,0.2)',
                },
              }}
            >
              {themeMode === 'dark' ? <Brightness7 /> : <Brightness4 />}
            </IconButton>

            {/* 用户登录/头像 */}
            {isAuthenticated ? (
              <>
                <IconButton
                  onClick={handleUserMenuOpen}
                  sx={{
                    bgcolor: themeMode === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',
                    '&:hover': {
                      bgcolor: themeMode === 'dark' ? 'rgba(255,255,255,0.2)' : 'rgba(0,0,0,0.2)',
                    },
                  }}
                >
                  <Avatar sx={{ width: 24, height: 24 }}>
                    {user?.name?.charAt(0) || user?.email?.charAt(0) || 'U'}
                  </Avatar>
                </IconButton>

                <Menu
                  anchorEl={userMenuAnchor}
                  open={Boolean(userMenuAnchor)}
                  onClose={handleUserMenuClose}
                  slotProps={{
                    paper: {
                      sx: {
                        bgcolor: themeMode === 'dark' ? '#2a2a2a' : '#fff',
                        border: `1px solid ${themeMode === 'dark' ? '#444' : '#e0e0e0'}`,
                      }
                    }
                  }}
                >
                  <MenuItem disabled>
                    <Box>
                      <Typography variant="subtitle2">{user?.name || '用户'}</Typography>
                      <Typography variant="caption" color="text.secondary">
                        {user?.email}
                      </Typography>
                    </Box>
                  </MenuItem>
                  <Divider />
                  <MenuItem onClick={handleUserMenuClose}>
                    <Settings sx={{ mr: 1 }} />
                    {t.common.settings}
                  </MenuItem>
                  <MenuItem onClick={handleLogout}>
                    <Logout sx={{ mr: 1 }} />
                    {t.common.logout}
                  </MenuItem>
                </Menu>
              </>
            ) : (
              <Button
                variant="contained"
                startIcon={<Login />}
                onClick={() => setLoginDialogOpen(true)}
                size="small"
                sx={{
                  bgcolor: themeMode === 'dark' ? '#1976d2' : '#1976d2',
                  '&:hover': {
                    bgcolor: themeMode === 'dark' ? '#1565c0' : '#1565c0',
                  },
                }}
              >
                {t.common.login}
              </Button>
            )}
          </Box>
        </Box>
      </Box>

      {/* 页面主体区域 */}
      <Box
        sx={{
          minHeight: '100vh',
          pt: '80px', // 为固定页眉留出空间
          bgcolor: themeMode === 'dark' ? '#121212' : '#f5f5f5',
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        {/* 主内容区域 - 真正的左右居中 */}
        <Box
          sx={{
            flex: 1,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'flex-start',
            p: 4,
          }}
        >
          <Box
            sx={{
              width: '100%',
              maxWidth: 1000,       // ✅ 改为 maxWidth，与页眉统一
              mx: 'auto',           // ✅ 中央对齐
              bgcolor: themeMode === 'dark' ? '#1e1e1e' : '#fff',
              borderRadius: 2,
              boxShadow: themeMode === 'dark'
                ? '0 8px 32px rgba(0,0,0,0.3)'
                : '0 8px 32px rgba(0,0,0,0.12)',
              p: 4,
              minHeight: 600,
            }}
          >
            {children}
          </Box>
        </Box>


        <Box
          sx={{
            p: 2,
            textAlign: 'center',
            mt: 'auto',
          }}
        >
          <Typography variant="caption" color="text.secondary">
            {t.footer.copyright}
          </Typography>
        </Box>
      </Box>

      {/* 登录对话框 */}
      <LoginDialog
        open={loginDialogOpen}
        onClose={() => setLoginDialogOpen(false)}
      />
    </>
  );
};
