import React from 'react';
import { Box, IconButton, Typography } from '@mui/material';
import { Brightness4, Brightness7, Language } from '@mui/icons-material';
import { useThemeStore } from '../stores/theme';
import { useLocaleStore } from '../stores/locale';

interface MainLayoutProps {
  children: React.ReactNode;
}

export const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const { mode: themeMode, toggleMode } = useThemeStore();
  const { locale, setLocale } = useLocaleStore();

  const handleThemeToggle = () => {
    toggleMode();
  };

  const handleLocaleToggle = () => {
    const newLocale = locale === 'en' ? 'zh' : 'en';
    setLocale(newLocale);
  };

  return (
    <Box
      sx={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        bgcolor: themeMode === 'dark' ? '#121212' : '#f5f5f5',
      }}
    >
      {/* 顶部工具栏 */}
      <Box
        sx={{
          position: 'fixed',
          top: 20,
          right: 20,
          display: 'flex',
          gap: 1,
          zIndex: 1000,
        }}
      >
        <IconButton
          onClick={handleLocaleToggle}
          sx={{
            bgcolor: themeMode === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',
            '&:hover': {
              bgcolor: themeMode === 'dark' ? 'rgba(255,255,255,0.2)' : 'rgba(0,0,0,0.2)',
            },
          }}
        >
          <Language />
        </IconButton>
        <IconButton
          onClick={handleThemeToggle}
          sx={{
            bgcolor: themeMode === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',
            '&:hover': {
              bgcolor: themeMode === 'dark' ? 'rgba(255,255,255,0.2)' : 'rgba(0,0,0,0.2)',
            },
          }}
        >
          {themeMode === 'dark' ? <Brightness7 /> : <Brightness4 />}
        </IconButton>
      </Box>

      {/* 主标题 */}
      <Typography
        variant="h3"
        sx={{
          mb: 6,
          fontWeight: 300,
          color: themeMode === 'dark' ? '#fff' : '#333',
          textAlign: 'center',
        }}
      >
        小野导航
      </Typography>

      {/* 主内容区域 - 真正居中 */}
      <Box
        sx={{
          width: 900,
          bgcolor: themeMode === 'dark' ? '#1e1e1e' : '#fff',
          borderRadius: 2,
          boxShadow: themeMode === 'dark'
            ? '0 8px 32px rgba(0,0,0,0.3)'
            : '0 8px 32px rgba(0,0,0,0.12)',
          p: 4,
          minHeight: 600,
        }}
      >
        {children}
      </Box>
    </Box>
  );
};
