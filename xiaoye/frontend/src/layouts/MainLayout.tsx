import React, { useState } from 'react';
import {
  Box,
  IconButton,
  Typography,
  Button,
  Avatar,
  Menu,
  MenuItem,
  Divider,
  Chip
} from '@mui/material';
import {
  Brightness4,
  Brightness7,
  Language,
  Login,
  Logout,
  Person,
  Settings,
  CloudSync
} from '@mui/icons-material';
import { useThemeStore } from '../stores/theme';
import { useLocaleStore } from '../stores/locale';
import { useAuthStore } from '../stores/auth';
import { LoginDialog } from '../components/LoginDialog';

interface MainLayoutProps {
  children: React.ReactNode;
}

export const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const { mode: themeMode, toggleMode } = useThemeStore();
  const { locale, setLocale } = useLocaleStore();
  const { user, isAuthenticated, logout } = useAuthStore();
  const [loginDialogOpen, setLoginDialogOpen] = useState(false);
  const [userMenuAnchor, setUserMenuAnchor] = useState<null | HTMLElement>(null);

  const handleThemeToggle = () => {
    toggleMode();
  };

  const handleLocaleToggle = () => {
    const newLocale = locale === 'en' ? 'zh' : 'en';
    setLocale(newLocale);
  };

  const handleUserMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setUserMenuAnchor(event.currentTarget);
  };

  const handleUserMenuClose = () => {
    setUserMenuAnchor(null);
  };

  const handleLogout = () => {
    logout();
    handleUserMenuClose();
  };

  return (
    <Box
      sx={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        bgcolor: themeMode === 'dark' ? '#121212' : '#f5f5f5',
      }}
    >
      {/* 顶部工具栏 */}
      <Box
        sx={{
          position: 'fixed',
          top: 20,
          right: 20,
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          zIndex: 1000,
        }}
      >
        {/* 用户状态显示 */}
        {isAuthenticated && (
          <Chip
            icon={<CloudSync />}
            label="已同步"
            size="small"
            color="success"
            sx={{
              bgcolor: themeMode === 'dark' ? 'rgba(76, 175, 80, 0.2)' : 'rgba(76, 175, 80, 0.1)',
              color: themeMode === 'dark' ? '#81c784' : '#4caf50',
              mr: 1,
            }}
          />
        )}

        <IconButton
          onClick={handleLocaleToggle}
          sx={{
            bgcolor: themeMode === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',
            '&:hover': {
              bgcolor: themeMode === 'dark' ? 'rgba(255,255,255,0.2)' : 'rgba(0,0,0,0.2)',
            },
          }}
        >
          <Language />
        </IconButton>

        <IconButton
          onClick={handleThemeToggle}
          sx={{
            bgcolor: themeMode === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',
            '&:hover': {
              bgcolor: themeMode === 'dark' ? 'rgba(255,255,255,0.2)' : 'rgba(0,0,0,0.2)',
            },
          }}
        >
          {themeMode === 'dark' ? <Brightness7 /> : <Brightness4 />}
        </IconButton>

        {/* 用户登录/头像 */}
        {isAuthenticated ? (
          <>
            <IconButton
              onClick={handleUserMenuOpen}
              sx={{
                bgcolor: themeMode === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',
                '&:hover': {
                  bgcolor: themeMode === 'dark' ? 'rgba(255,255,255,0.2)' : 'rgba(0,0,0,0.2)',
                },
              }}
            >
              <Avatar sx={{ width: 24, height: 24 }}>
                {user?.name?.charAt(0) || user?.email?.charAt(0) || 'U'}
              </Avatar>
            </IconButton>

            <Menu
              anchorEl={userMenuAnchor}
              open={Boolean(userMenuAnchor)}
              onClose={handleUserMenuClose}
              PaperProps={{
                sx: {
                  bgcolor: themeMode === 'dark' ? '#2a2a2a' : '#fff',
                  border: `1px solid ${themeMode === 'dark' ? '#444' : '#e0e0e0'}`,
                }
              }}
            >
              <MenuItem disabled>
                <Box>
                  <Typography variant="subtitle2">{user?.name || '用户'}</Typography>
                  <Typography variant="caption" color="text.secondary">
                    {user?.email}
                  </Typography>
                </Box>
              </MenuItem>
              <Divider />
              <MenuItem onClick={handleUserMenuClose}>
                <Settings sx={{ mr: 1 }} />
                设置
              </MenuItem>
              <MenuItem onClick={handleLogout}>
                <Logout sx={{ mr: 1 }} />
                退出登录
              </MenuItem>
            </Menu>
          </>
        ) : (
          <Button
            variant="contained"
            startIcon={<Login />}
            onClick={() => setLoginDialogOpen(true)}
            size="small"
            sx={{
              bgcolor: themeMode === 'dark' ? '#1976d2' : '#1976d2',
              '&:hover': {
                bgcolor: themeMode === 'dark' ? '#1565c0' : '#1565c0',
              },
            }}
          >
            登录
          </Button>
        )}
      </Box>

      {/* 登录对话框 */}
      <LoginDialog
        open={loginDialogOpen}
        onClose={() => setLoginDialogOpen(false)}
      />

      {/* 主标题 */}
      <Box sx={{ textAlign: 'center', mb: 6 }}>
        <Typography
          variant="h3"
          sx={{
            mb: 2,
            fontWeight: 300,
            color: themeMode === 'dark' ? '#fff' : '#333',
          }}
        >
          📚 小野导航
        </Typography>
        <Typography
          variant="body2"
          sx={{
            color: themeMode === 'dark' ? '#aaa' : '#666',
            mb: 1
          }}
        >
          个人书签管理工具
        </Typography>
        <Typography
          variant="caption"
          sx={{
            color: themeMode === 'dark' ? '#666' : '#999'
          }}
        >
          v1.0.0 · 简约版
        </Typography>
      </Box>

      {/* 主内容区域 - 真正居中 */}
      <Box
        sx={{
          width: 900,
          bgcolor: themeMode === 'dark' ? '#1e1e1e' : '#fff',
          borderRadius: 2,
          boxShadow: themeMode === 'dark'
            ? '0 8px 32px rgba(0,0,0,0.3)'
            : '0 8px 32px rgba(0,0,0,0.12)',
          p: 4,
          minHeight: 600,
        }}
      >
        {children}
      </Box>
    </Box>
  );
};
