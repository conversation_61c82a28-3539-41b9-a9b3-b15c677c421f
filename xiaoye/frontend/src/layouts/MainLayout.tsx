import React from 'react';
import {
  AppBar,
  Toolbar,
  Typography,
  IconButton,
  Box,
  Container,
} from '@mui/material';
import {
  Brightness4,
  Brightness7,
  Language,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import { useThemeStore } from '../stores/theme';
import { useLocaleStore } from '../stores/locale';

interface MainLayoutProps {
  children: React.ReactNode;
}

export const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const { mode: themeMode, toggleMode } = useThemeStore();
  const { locale, setLocale } = useLocaleStore();

  const handleThemeToggle = () => {
    toggleMode();
  };

  const handleLocaleToggle = () => {
    const newLocale = locale === 'en' ? 'zh' : 'en';
    setLocale(newLocale);
  };

  return (
    <Box sx={{
      minHeight: '100vh',
      display: 'flex',
      flexDirection: 'column',
      bgcolor: 'background.default'
    }}>
      <AppBar position="static" elevation={1}>
        <Container maxWidth="xl">
          <Toolbar sx={{ px: { xs: 0, sm: 2 } }}>
            <Typography
              variant="h6"
              component="div"
              sx={{
                flexGrow: 1,
                fontSize: { xs: '1.1rem', sm: '1.25rem' }
              }}
            >
              小野在线导航
            </Typography>

            <IconButton
              color="inherit"
              onClick={handleLocaleToggle}
              size="large"
              sx={{ ml: 1 }}
            >
              <Language />
            </IconButton>

            <IconButton
              color="inherit"
              onClick={handleThemeToggle}
              size="large"
              sx={{ ml: 1 }}
            >
              {themeMode === 'dark' ? <Brightness7 /> : <Brightness4 />}
            </IconButton>

            <IconButton
              color="inherit"
              size="large"
              sx={{ ml: 1 }}
            >
              <SettingsIcon />
            </IconButton>
          </Toolbar>
        </Container>
      </AppBar>

      <Container
        maxWidth="xl"
        sx={{
          flex: 1,
          py: { xs: 2, sm: 3, md: 4 },
          px: { xs: 2, sm: 3, md: 4 }
        }}
      >
        {children}
      </Container>
    </Box>
  );
};
