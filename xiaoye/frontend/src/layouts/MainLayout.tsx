import React from 'react';
import {
  AppBar,
  Toolbar,
  Typography,
  IconButton,
  Box,
  Container,
} from '@mui/material';
import {
  Brightness4,
  Brightness7,
  Language,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import { useThemeStore } from '../stores/theme';
import { useLocaleStore } from '../stores/locale';

interface MainLayoutProps {
  children: React.ReactNode;
}

export const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const { mode: themeMode, toggleMode } = useThemeStore();
  const { locale, setLocale } = useLocaleStore();

  const handleThemeToggle = () => {
    toggleMode();
  };

  const handleLocaleToggle = () => {
    const newLocale = locale === 'en' ? 'zh' : 'en';
    setLocale(newLocale);
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      <AppBar position="static" elevation={1}>
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            小野在线
          </Typography>

          <IconButton color="inherit" onClick={handleLocaleToggle}>
            <Language />
          </IconButton>

          <IconButton color="inherit" onClick={handleThemeToggle}>
            {themeMode === 'dark' ? <Brightness7 /> : <Brightness4 />}
          </IconButton>

          <IconButton color="inherit">
            <SettingsIcon />
          </IconButton>
        </Toolbar>
      </AppBar>

      <Container maxWidth="xl" sx={{ mt: 2, mb: 2 }}>
        {children}
      </Container>
    </Box>
  );
};
