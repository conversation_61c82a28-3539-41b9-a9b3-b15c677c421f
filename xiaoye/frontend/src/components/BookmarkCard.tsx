import React from 'react';
import {
  Card,
  CardContent,
  CardActions,
  Typography,
  IconButton,
  Avatar,
  Box,
  Tooltip,
} from '@mui/material';
import {
  Edit,
  Delete,
  Launch,
  Star,
  StarBorder,
} from '@mui/icons-material';
import { Bookmark } from '../services/api';

interface BookmarkCardProps {
  bookmark: Bookmark;
  onEdit?: (bookmark: Bookmark) => void;
  onDelete?: (bookmark: Bookmark) => void;
  onToggleQuickAccess?: (bookmark: Bookmark) => void;
  showActions?: boolean;
}

export const BookmarkCard: React.FC<BookmarkCardProps> = ({
  bookmark,
  onEdit,
  onDelete,
  onToggleQuickAccess,
  showActions = true,
}) => {
  const handleClick = () => {
    window.open(bookmark.url, '_blank', 'noopener,noreferrer');
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    onEdit?.(bookmark);
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDelete?.(bookmark);
  };

  const handleToggleQuickAccess = (e: React.MouseEvent) => {
    e.stopPropagation();
    onToggleQuickAccess?.(bookmark);
  };

  return (
    <Card
      sx={{
        cursor: 'pointer',
        transition: 'all 0.2s ease-in-out',
        '&:hover': {
          transform: 'translateY(-2px)',
          boxShadow: 4,
        },
      }}
      onClick={handleClick}
    >
      <CardContent sx={{ pb: showActions ? 1 : 2 }}>
        <Box display="flex" alignItems="center" mb={1}>
          <Avatar
            src={bookmark.favicon || bookmark.icon}
            sx={{ width: 24, height: 24, mr: 1 }}
          >
            <Launch fontSize="small" />
          </Avatar>
          <Typography variant="h6" component="h3" noWrap sx={{ flexGrow: 1 }}>
            {bookmark.title}
          </Typography>
          {bookmark.isQuickAccess && (
            <Tooltip title="快捷访问">
              <Star color="primary" fontSize="small" />
            </Tooltip>
          )}
        </Box>
        
        {bookmark.description && (
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical',
              overflow: 'hidden',
            }}
          >
            {bookmark.description}
          </Typography>
        )}
        
        <Typography
          variant="caption"
          color="text.secondary"
          sx={{
            display: 'block',
            mt: 1,
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
          }}
        >
          {bookmark.url}
        </Typography>
      </CardContent>
      
      {showActions && (
        <CardActions sx={{ pt: 0, justifyContent: 'flex-end' }}>
          <Tooltip title={bookmark.isQuickAccess ? '取消快捷访问' : '添加到快捷访问'}>
            <IconButton size="small" onClick={handleToggleQuickAccess}>
              {bookmark.isQuickAccess ? <Star /> : <StarBorder />}
            </IconButton>
          </Tooltip>
          <Tooltip title="编辑">
            <IconButton size="small" onClick={handleEdit}>
              <Edit />
            </IconButton>
          </Tooltip>
          <Tooltip title="删除">
            <IconButton size="small" onClick={handleDelete}>
              <Delete />
            </IconButton>
          </Tooltip>
        </CardActions>
      )}
    </Card>
  );
};
