import React from 'react';
import {
  Card,
  CardContent,
  CardActions,
  Typography,
  IconButton,
  Avatar,
  Box,
  Tooltip,
} from '@mui/material';
import {
  Edit,
  Delete,
  Launch,
  Star,
  StarBorder,
} from '@mui/icons-material';
// 书签类型定义
interface Bookmark {
  id: string;
  title: string;
  url: string;
  description?: string;
  icon?: string;
  favicon?: string;
  sortOrder: number;
  isVisible: boolean;
  isQuickAccess: boolean;
  userId: string;
  categoryId?: string;
  createdAt: string;
  updatedAt: string;
}

interface BookmarkCardProps {
  bookmark: Bookmark;
  onEdit?: (bookmark: Bookmark) => void;
  onDelete?: (bookmark: Bookmark) => void;
  onToggleQuickAccess?: (bookmark: Bookmark) => void;
  showActions?: boolean;
}

export const BookmarkCard: React.FC<BookmarkCardProps> = ({
  bookmark,
  onEdit,
  onDelete,
  onToggleQuickAccess,
  showActions = true,
}) => {
  const handleClick = () => {
    window.open(bookmark.url, '_blank', 'noopener,noreferrer');
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    onEdit?.(bookmark);
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDelete?.(bookmark);
  };

  const handleToggleQuickAccess = (e: React.MouseEvent) => {
    e.stopPropagation();
    onToggleQuickAccess?.(bookmark);
  };

  return (
    <Card
      sx={{
        cursor: 'pointer',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        borderRadius: { xs: 2, sm: 3 },
        boxShadow: { xs: 1, sm: 2 },
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: { xs: 4, sm: 8 },
        },
        '&:active': {
          transform: 'translateY(-2px)',
        },
      }}
      onClick={handleClick}
    >
      <CardContent
        sx={{
          pb: showActions ? 1 : 2,
          px: { xs: 2, sm: 3 },
          pt: { xs: 2, sm: 3 },
          flex: 1,
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        <Box display="flex" alignItems="center" mb={{ xs: 1, sm: 1.5 }}>
          <Avatar
            src={bookmark.favicon || bookmark.icon}
            sx={{
              width: { xs: 28, sm: 32 },
              height: { xs: 28, sm: 32 },
              mr: { xs: 1, sm: 1.5 },
              bgcolor: 'primary.light'
            }}
          >
            <Launch fontSize="small" />
          </Avatar>
          <Typography
            variant="h6"
            component="h3"
            noWrap
            sx={{
              flexGrow: 1,
              fontSize: { xs: '1rem', sm: '1.125rem' },
              fontWeight: 600
            }}
          >
            {bookmark.title}
          </Typography>
          {bookmark.isQuickAccess && (
            <Tooltip title="快捷访问">
              <Star
                color="primary"
                sx={{ fontSize: { xs: 18, sm: 20 } }}
              />
            </Tooltip>
          )}
        </Box>

        {bookmark.description && (
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical',
              overflow: 'hidden',
              mb: 1,
              fontSize: { xs: '0.875rem', sm: '0.875rem' },
              lineHeight: 1.4,
              flex: 1
            }}
          >
            {bookmark.description}
          </Typography>
        )}

        <Typography
          variant="caption"
          color="text.secondary"
          sx={{
            display: 'block',
            mt: 'auto',
            pt: 1,
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            fontSize: { xs: '0.75rem', sm: '0.75rem' },
            opacity: 0.7
          }}
        >
          {bookmark.url}
        </Typography>
      </CardContent>

      {showActions && (
        <CardActions
          sx={{
            pt: 0,
            px: { xs: 2, sm: 3 },
            pb: { xs: 1.5, sm: 2 },
            justifyContent: 'flex-end',
            gap: 0.5
          }}
        >
          <Tooltip title={bookmark.isQuickAccess ? '取消快捷访问' : '添加到快捷访问'}>
            <IconButton
              size="small"
              onClick={handleToggleQuickAccess}
              sx={{
                color: bookmark.isQuickAccess ? 'primary.main' : 'text.secondary',
                '&:hover': {
                  bgcolor: 'primary.light',
                  color: 'primary.main'
                }
              }}
            >
              {bookmark.isQuickAccess ? <Star /> : <StarBorder />}
            </IconButton>
          </Tooltip>
          <Tooltip title="编辑">
            <IconButton
              size="small"
              onClick={handleEdit}
              sx={{
                color: 'text.secondary',
                '&:hover': {
                  bgcolor: 'info.light',
                  color: 'info.main'
                }
              }}
            >
              <Edit />
            </IconButton>
          </Tooltip>
          <Tooltip title="删除">
            <IconButton
              size="small"
              onClick={handleDelete}
              sx={{
                color: 'text.secondary',
                '&:hover': {
                  bgcolor: 'error.light',
                  color: 'error.main'
                }
              }}
            >
              <Delete />
            </IconButton>
          </Tooltip>
        </CardActions>
      )}
    </Card>
  );
};
