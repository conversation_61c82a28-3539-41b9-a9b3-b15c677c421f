import React from 'react';
import {
  <PERSON>,
  Typography,
  Grid,
  Chip,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Edit,
  Delete,
  Add,
} from '@mui/icons-material';
import { BookmarkCard } from './BookmarkCard';

// 类型定义
interface Category {
  id: string;
  name: string;
  description?: string;
  icon?: string;
  color?: string;
  sortOrder: number;
  isVisible: boolean;
  isQuickAccess: boolean;
  userId: string;
  createdAt: string;
  updatedAt: string;
}

interface Bookmark {
  id: string;
  title: string;
  url: string;
  description?: string;
  icon?: string;
  favicon?: string;
  sortOrder: number;
  isVisible: boolean;
  isQuickAccess: boolean;
  userId: string;
  categoryId?: string;
  createdAt: string;
  updatedAt: string;
}

interface CategorySectionProps {
  category: Category;
  bookmarks: Bookmark[];
  onEditCategory?: (category: Category) => void;
  onDeleteCategory?: (category: Category) => void;
  onAddBookmark?: (categoryId: string) => void;
  onEditBookmark?: (bookmark: Bookmark) => void;
  onDeleteBookmark?: (bookmark: Bookmark) => void;
  onToggleBookmarkQuickAccess?: (bookmark: Bookmark) => void;
}

export const CategorySection: React.FC<CategorySectionProps> = ({
  category,
  bookmarks,
  onEditCategory,
  onDeleteCategory,
  onAddBookmark,
  onEditBookmark,
  onDeleteBookmark,
  onToggleBookmarkQuickAccess,
}) => {
  return (
    <Box sx={{ mb: 4 }}>
      <Box display="flex" alignItems="center" mb={2}>
        <Chip
          label={category.name}
          sx={{
            backgroundColor: category.color || '#1976d2',
            color: 'white',
            fontWeight: 'bold',
            mr: 1,
          }}
        />

        <Typography variant="body2" color="text.secondary" sx={{ flexGrow: 1 }}>
          {category.description}
        </Typography>

        <Tooltip title="添加书签">
          <IconButton
            size="small"
            onClick={() => onAddBookmark?.(category.id)}
          >
            <Add />
          </IconButton>
        </Tooltip>

        <Tooltip title="编辑分类">
          <IconButton
            size="small"
            onClick={() => onEditCategory?.(category)}
          >
            <Edit />
          </IconButton>
        </Tooltip>

        <Tooltip title="删除分类">
          <IconButton
            size="small"
            onClick={() => onDeleteCategory?.(category)}
          >
            <Delete />
          </IconButton>
        </Tooltip>
      </Box>

      {bookmarks.length > 0 ? (
        <Grid container spacing={2}>
          {bookmarks.map((bookmark) => (
            <Grid item xs={12} sm={6} md={4} lg={3} key={bookmark.id}>
              <BookmarkCard
                bookmark={bookmark}
                onEdit={onEditBookmark}
                onDelete={onDeleteBookmark}
                onToggleQuickAccess={onToggleBookmarkQuickAccess}
              />
            </Grid>
          ))}
        </Grid>
      ) : (
        <Box
          sx={{
            textAlign: 'center',
            py: 4,
            color: 'text.secondary',
            border: '2px dashed',
            borderColor: 'divider',
            borderRadius: 1,
          }}
        >
          <Typography variant="body2">
            此分类暂无书签，点击上方 + 按钮添加书签
          </Typography>
        </Box>
      )}
    </Box>
  );
};
