import React from 'react';
import {
  <PERSON>,
  Typography,
  Grid,
  Chip,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Edit,
  Delete,
  Add,
} from '@mui/icons-material';
import { BookmarkCard } from './BookmarkCard';

// 类型定义
interface Category {
  id: string;
  name: string;
  description?: string;
  icon?: string;
  color?: string;
  sortOrder: number;
  isVisible: boolean;
  isQuickAccess: boolean;
  userId: string;
  createdAt: string;
  updatedAt: string;
}

interface Bookmark {
  id: string;
  title: string;
  url: string;
  description?: string;
  icon?: string;
  favicon?: string;
  sortOrder: number;
  isVisible: boolean;
  isQuickAccess: boolean;
  userId: string;
  categoryId?: string;
  createdAt: string;
  updatedAt: string;
}

interface CategorySectionProps {
  category: Category;
  bookmarks: Bookmark[];
  onEditCategory?: (category: Category) => void;
  onDeleteCategory?: (category: Category) => void;
  onAddBookmark?: (categoryId: string) => void;
  onEditBookmark?: (bookmark: Bookmark) => void;
  onDeleteBookmark?: (bookmark: Bookmark) => void;
  onToggleBookmarkQuickAccess?: (bookmark: Bookmark) => void;
}

export const CategorySection: React.FC<CategorySectionProps> = ({
  category,
  bookmarks,
  onEditCategory,
  onDeleteCategory,
  onAddBookmark,
  onEditBookmark,
  onDeleteBookmark,
  onToggleBookmarkQuickAccess,
}) => {
  return (
    <Box sx={{ mb: { xs: 4, md: 6 } }}>
      <Box
        display="flex"
        alignItems={{ xs: 'flex-start', sm: 'center' }}
        flexDirection={{ xs: 'column', sm: 'row' }}
        gap={{ xs: 2, sm: 0 }}
        mb={{ xs: 2, sm: 3 }}
        p={{ xs: 2, sm: 3 }}
        bgcolor="background.paper"
        borderRadius={2}
        boxShadow={1}
      >
        <Box display="flex" alignItems="center" sx={{ flexGrow: 1 }}>
          <Chip
            label={category.name}
            icon={category.icon ? <span>{category.icon}</span> : undefined}
            sx={{
              backgroundColor: category.color || 'primary.main',
              color: 'white',
              fontWeight: 600,
              fontSize: { xs: '0.875rem', sm: '1rem' },
              height: { xs: 32, sm: 36 },
              mr: { xs: 0, sm: 2 },
              mb: { xs: 1, sm: 0 },
              '& .MuiChip-label': {
                px: { xs: 1.5, sm: 2 }
              }
            }}
          />

          {category.description && (
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{
                display: { xs: 'block', sm: 'block' },
                fontSize: { xs: '0.875rem', sm: '0.875rem' },
                mt: { xs: 0, sm: 0 }
              }}
            >
              {category.description}
            </Typography>
          )}
        </Box>

        <Box
          display="flex"
          gap={0.5}
          sx={{
            alignSelf: { xs: 'flex-end', sm: 'center' },
            mt: { xs: 1, sm: 0 }
          }}
        >
          <Tooltip title="添加书签">
            <IconButton
              size="small"
              onClick={() => onAddBookmark?.(category.id)}
              sx={{
                color: 'text.secondary',
                '&:hover': {
                  bgcolor: 'success.light',
                  color: 'success.main'
                }
              }}
            >
              <Add />
            </IconButton>
          </Tooltip>

          <Tooltip title="编辑分类">
            <IconButton
              size="small"
              onClick={() => onEditCategory?.(category)}
              sx={{
                color: 'text.secondary',
                '&:hover': {
                  bgcolor: 'info.light',
                  color: 'info.main'
                }
              }}
            >
              <Edit />
            </IconButton>
          </Tooltip>

          <Tooltip title="删除分类">
            <IconButton
              size="small"
              onClick={() => onDeleteCategory?.(category)}
              sx={{
                color: 'text.secondary',
                '&:hover': {
                  bgcolor: 'error.light',
                  color: 'error.main'
                }
              }}
            >
              <Delete />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {bookmarks.length > 0 ? (
        <Grid container spacing={{ xs: 2, sm: 3, md: 4 }}>
          {bookmarks.map((bookmark) => (
            <Grid
              item
              xs={12}
              sm={6}
              md={4}
              lg={3}
              xl={2.4}
              key={bookmark.id}
            >
              <BookmarkCard
                bookmark={bookmark}
                onEdit={onEditBookmark}
                onDelete={onDeleteBookmark}
                onToggleQuickAccess={onToggleBookmarkQuickAccess}
              />
            </Grid>
          ))}
        </Grid>
      ) : (
        <Box
          sx={{
            textAlign: 'center',
            py: { xs: 4, sm: 6 },
            px: 2,
            color: 'text.secondary',
            border: '2px dashed',
            borderColor: 'divider',
            borderRadius: 2,
            bgcolor: 'background.default',
            '&:hover': {
              borderColor: 'primary.light',
              bgcolor: 'action.hover'
            }
          }}
        >
          <Typography
            variant="body1"
            sx={{
              fontSize: { xs: '0.875rem', sm: '1rem' },
              mb: 2
            }}
          >
            📚 此分类暂无书签
          </Typography>
          <Typography
            variant="body2"
            sx={{
              fontSize: { xs: '0.75rem', sm: '0.875rem' },
              opacity: 0.8
            }}
          >
            点击上方 ➕ 按钮添加书签
          </Typography>
        </Box>
      )}
    </Box>
  );
};
