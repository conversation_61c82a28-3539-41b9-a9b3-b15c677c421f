import axios from 'axios';

const API_BASE_URL = 'http://localhost:3000/api';

export const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 类型定义
export interface Bookmark {
  id: string;
  title: string;
  url: string;
  description?: string;
  icon?: string;
  favicon?: string;
  sortOrder: number;
  isVisible: boolean;
  isQuickAccess: boolean;
  userId: string;
  categoryId?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Category {
  id: string;
  name: string;
  description?: string;
  icon?: string;
  color?: string;
  sortOrder: number;
  isVisible: boolean;
  isQuickAccess: boolean;
  userId: string;
  createdAt: string;
  updatedAt: string;
}

// 书签API
export const bookmarkApi = {
  getAll: (userId?: string) => api.get<Bookmark[]>('/bookmarks', { params: { userId } }),
  getById: (id: string) => api.get<Bookmark>(`/bookmarks/${id}`),
  getQuickAccess: (userId?: string) => api.get<Bookmark[]>('/bookmarks/quick-access', { params: { userId } }),
  getByCategory: (categoryId: string, userId?: string) => 
    api.get<Bookmark[]>(`/bookmarks/category/${categoryId}`, { params: { userId } }),
  create: (data: Omit<Bookmark, 'id' | 'createdAt' | 'updatedAt'>, userId?: string) => 
    api.post<Bookmark>('/bookmarks', data, { params: { userId } }),
  update: (id: string, data: Partial<Bookmark>, userId?: string) => 
    api.patch<Bookmark>(`/bookmarks/${id}`, data, { params: { userId } }),
  delete: (id: string, userId?: string) => 
    api.delete(`/bookmarks/${id}`, { params: { userId } }),
};

// 分类API
export const categoryApi = {
  getAll: (userId?: string) => api.get<Category[]>('/categories', { params: { userId } }),
  getById: (id: string) => api.get<Category>(`/categories/${id}`),
  getQuickAccess: (userId?: string) => api.get<Category[]>('/categories/quick-access', { params: { userId } }),
  create: (data: Omit<Category, 'id' | 'createdAt' | 'updatedAt'>, userId?: string) => 
    api.post<Category>('/categories', data, { params: { userId } }),
  update: (id: string, data: Partial<Category>, userId?: string) => 
    api.patch<Category>(`/categories/${id}`, data, { params: { userId } }),
  delete: (id: string, userId?: string) => 
    api.delete(`/categories/${id}`, { params: { userId } }),
};
