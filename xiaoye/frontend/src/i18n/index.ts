interface Translations {
  // 通用
  common: {
    add: string;
    delete: string;
    edit: string;
    save: string;
    cancel: string;
    confirm: string;
    loading: string;
    search: string;
    settings: string;
    logout: string;
    login: string;
    register: string;
  };

  // 导航
  nav: {
    title: string;
    subtitle: string;
    version: string;
    syncStatus: string;
    localStorage: string;
    cloudSync: string;
    loginPrompt: string;
  };

  // 书签
  bookmarks: {
    title: string;
    addBookmark: string;
    editBookmark: string;
    deleteBookmark: string;
    bookmarkTitle: string;
    bookmarkUrl: string;
    bookmarkDescription: string;
    bookmarkCategory: string;
    totalCount: string;
    emptyState: string;
    emptyDescription: string;
    openInNewTab: string;
    deleteConfirm: string;
    defaultBookmarkWarning: string;
    categoryPlaceholder: string;
  };

  // 认证
  auth: {
    loginTitle: string;
    registerTitle: string;
    email: string;
    password: string;
    confirmPassword: string;
    name: string;
    loginButton: string;
    registerButton: string;
    loginTab: string;
    registerTab: string;
    testAccount: string;
    passwordMismatch: string;
    passwordTooShort: string;
    loginFailed: string;
    registerFailed: string;
    adminOnly: string;
  };

  // 分类
  categories: {
    news: string;
    video: string;
    gaming: string;
    social: string;
    shopping: string;
    search: string;
    tools: string;
    education: string;
    music: string;
    myBookmarks: string;
    uncategorized: string;
  };

  // 页脚
  footer: {
    copyright: string;
  };
}

export const zhTranslations: Translations = {
  common: {
    add: '添加',
    delete: '删除',
    edit: '编辑',
    save: '保存',
    cancel: '取消',
    confirm: '确认',
    loading: '加载中...',
    search: '搜索',
    settings: '设置',
    logout: '退出登录',
    login: '登录',
    register: '注册',
  },

  nav: {
    title: '📚 小野导航',
    subtitle: '个人书签管理工具',
    version: 'v1.0.0 · 简约版',
    syncStatus: '已同步',
    localStorage: '本地存储',
    cloudSync: '云端同步',
    loginPrompt: '(登录后可云端同步)',
  },

  bookmarks: {
    title: '我的书签收藏',
    addBookmark: '添加新书签',
    editBookmark: '编辑书签',
    deleteBookmark: '删除书签',
    bookmarkTitle: '标题',
    bookmarkUrl: '网址',
    bookmarkDescription: '描述 (可选)',
    bookmarkCategory: '分类 (可选)',
    totalCount: '共 {count} 个书签',
    emptyState: '📚 还没有收藏任何书签',
    emptyDescription: '添加您常用的网站，自动分组管理',
    openInNewTab: '在新标签页打开',
    deleteConfirm: '确定要删除这个书签吗？',
    defaultBookmarkWarning: '默认书签无法删除，登录后可以隐藏不需要的默认书签',
    categoryPlaceholder: '例如：常用网站、工作、学习等',
  },

  auth: {
    loginTitle: '登录账号',
    registerTitle: '注册账号',
    email: '邮箱',
    password: '密码',
    confirmPassword: '确认密码',
    name: '姓名',
    loginButton: '登录',
    registerButton: '注册',
    loginTab: '登录',
    registerTab: '注册',
    testAccount: '使用测试账号登录',
    passwordMismatch: '两次输入的密码不一致',
    passwordTooShort: '密码长度至少6位',
    loginFailed: '登录失败，请检查邮箱和密码',
    registerFailed: '注册失败，请稍后重试',
    adminOnly: '只有管理员可以访问此系统',
  },

  categories: {
    news: '新闻资讯',
    video: '视频网站',
    gaming: '游戏直播',
    social: '社交媒体',
    shopping: '购物网站',
    search: '搜索引擎',
    tools: '工具网站',
    education: '学习教育',
    music: '音乐网站',
    myBookmarks: '我的收藏',
    uncategorized: '未分类',
  },

  footer: {
    copyright: '© 2024 小野导航 v1.0.0 · 简约版 · 个人书签管理工具',
  },
};

export const enTranslations: Translations = {
  common: {
    add: 'Add',
    delete: 'Delete',
    edit: 'Edit',
    save: 'Save',
    cancel: 'Cancel',
    confirm: 'Confirm',
    loading: 'Loading...',
    search: 'Search',
    settings: 'Settings',
    logout: 'Logout',
    login: 'Login',
    register: 'Register',
  },

  nav: {
    title: '📚 Xiaoye Navigation',
    subtitle: 'Personal Bookmark Manager',
    version: 'v1.0.0 · Simple',
    syncStatus: 'Synced',
    localStorage: 'Local Storage',
    cloudSync: 'Cloud Sync',
    loginPrompt: '(Login for cloud sync)',
  },

  bookmarks: {
    title: 'My Bookmark Collection',
    addBookmark: 'Add New Bookmark',
    editBookmark: 'Edit Bookmark',
    deleteBookmark: 'Delete Bookmark',
    bookmarkTitle: 'Title',
    bookmarkUrl: 'URL',
    bookmarkDescription: 'Description (Optional)',
    bookmarkCategory: 'Category (Optional)',
    totalCount: 'Total {count} bookmarks',
    emptyState: '📚 No bookmarks yet',
    emptyDescription: 'Add your favorite websites with auto-grouping',
    openInNewTab: 'Open in new tab',
    deleteConfirm: 'Are you sure to delete this bookmark?',
    defaultBookmarkWarning: 'Default bookmarks cannot be deleted. Login to hide unwanted default bookmarks.',
    categoryPlaceholder: 'e.g.: Favorites, Work, Study, etc.',
  },

  auth: {
    loginTitle: 'Login Account',
    registerTitle: 'Register Account',
    email: 'Email',
    password: 'Password',
    confirmPassword: 'Confirm Password',
    name: 'Name',
    loginButton: 'Login',
    registerButton: 'Register',
    loginTab: 'Login',
    registerTab: 'Register',
    testAccount: 'Use test account',
    passwordMismatch: 'Passwords do not match',
    passwordTooShort: 'Password must be at least 6 characters',
    loginFailed: 'Login failed, please check email and password',
    registerFailed: 'Registration failed, please try again',
    adminOnly: 'Only administrators can access this system',
  },

  categories: {
    news: 'News',
    video: 'Video',
    gaming: 'Gaming & Live',
    social: 'Social Media',
    shopping: 'Shopping',
    search: 'Search Engines',
    tools: 'Tools',
    education: 'Education',
    music: 'Music',
    myBookmarks: 'My Bookmarks',
    uncategorized: 'Uncategorized',
  },

  footer: {
    copyright: '© 2024 Xiaoye Navigation v1.0.0 · Simple · Personal Bookmark Manager',
  },
};

export const translations = {
  zh: zhTranslations,
  en: enTranslations,
};

export type { Translations };
