import React from 'react';
import { ThemeProvider, CssBaseline, Container, Typography, Box } from '@mui/material';
import { lightTheme } from './themes';

function App() {
  return (
    <ThemeProvider theme={lightTheme}>
      <CssBaseline />
      <Container maxWidth="lg">
        <Box sx={{ my: 4 }}>
          <Typography variant="h3" component="h1" gutterBottom>
            小野在线 - 网址导航平台
          </Typography>
          <Typography variant="h6" color="text.secondary">
            项目基础架构已搭建完成！
          </Typography>
          <Typography variant="body1" sx={{ mt: 2 }}>
            ✅ 前端React+Vite+TypeScript项目已创建
          </Typography>
          <Typography variant="body1">
            ✅ MUI主题系统已配置
          </Typography>
          <Typography variant="body1">
            ✅ 后端NestJS API已启动 (http://localhost:3000/api)
          </Typography>
        </Box>
      </Container>
    </ThemeProvider>
  );
}

export default App;
