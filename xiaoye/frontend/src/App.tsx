import React, { useEffect } from 'react';
import { RouterProvider } from 'react-router-dom';
import { RecoilRoot, useRecoilState } from 'recoil';
import { ThemeProvider, CssBaseline } from '@mui/material';
import { lightTheme, darkTheme } from './themes';
import { themeModeState, getInitialThemeMode } from './recoil/atoms/theme';
import { localeState, getInitialLocale } from './recoil/atoms/locale';
import { router } from './router';

// 主题模式类型
type ThemeMode = 'light' | 'dark';

// 主题提供者组件
const AppThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [themeMode, setThemeMode] = useRecoilState(themeModeState);
  const [locale, setLocale] = useRecoilState(localeState);

  // 初始化主题和语言设置
  useEffect(() => {
    const initialTheme = getInitialThemeMode();
    const initialLocale = getInitialLocale();

    setThemeMode(initialTheme);
    setLocale(initialLocale);
  }, [setThemeMode, setLocale]);

  const theme = themeMode === 'light' ? lightTheme : darkTheme;

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      {children}
    </ThemeProvider>
  );
};

function App() {
  return (
    <RecoilRoot>
      <AppThemeProvider>
        <RouterProvider router={router} />
      </AppThemeProvider>
    </RecoilRoot>
  );
}

export default App;
