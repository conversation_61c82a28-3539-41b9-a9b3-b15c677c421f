import { useLocaleStore } from '../stores/locale';
import { translations, Translations } from '../i18n';

export const useTranslation = () => {
  const { locale } = useLocaleStore();
  
  const t = translations[locale as keyof typeof translations] as Translations;
  
  // 支持插值的翻译函数
  const translate = (key: string, params?: Record<string, string | number>): string => {
    const keys = key.split('.');
    let value: any = t;
    
    for (const k of keys) {
      value = value?.[k];
    }
    
    if (typeof value !== 'string') {
      return key; // 如果找不到翻译，返回原key
    }
    
    if (params) {
      return value.replace(/\{(\w+)\}/g, (match: string, paramKey: string) => {
        return params[paramKey]?.toString() || match;
      });
    }
    
    return value;
  };
  
  return { t, translate };
};
