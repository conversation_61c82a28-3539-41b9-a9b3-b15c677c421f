export interface DefaultBookmark {
  id: string;
  title: string;
  url: string;
  description: string;
  category: string;
}

export const defaultBookmarks: DefaultBookmark[] = [
  // 新闻资讯
  {
    id: 'news-1',
    title: '新浪新闻',
    url: 'https://news.sina.com.cn',
    description: '综合新闻资讯平台',
    category: '新闻资讯'
  },
  {
    id: 'news-2',
    title: '网易新闻',
    url: 'https://news.163.com',
    description: '网易新闻中心',
    category: '新闻资讯'
  },
  {
    id: 'news-3',
    title: '腾讯新闻',
    url: 'https://news.qq.com',
    description: '腾讯新闻频道',
    category: '新闻资讯'
  },
  {
    id: 'news-4',
    title: '澎湃新闻',
    url: 'https://www.thepaper.cn',
    description: '专业时政新闻',
    category: '新闻资讯'
  },

  // 视频网站
  {
    id: 'video-1',
    title: 'B站',
    url: 'https://www.bilibili.com',
    description: '哔哩哔哩弹幕视频网',
    category: '视频网站'
  },
  {
    id: 'video-2',
    title: '爱奇艺',
    url: 'https://www.iqiyi.com',
    description: '在线视频平台',
    category: '视频网站'
  },
  {
    id: 'video-3',
    title: '腾讯视频',
    url: 'https://v.qq.com',
    description: '腾讯视频官网',
    category: '视频网站'
  },
  {
    id: 'video-4',
    title: '优酷',
    url: 'https://www.youku.com',
    description: '优酷视频网站',
    category: '视频网站'
  },

  // 游戏直播
  {
    id: 'live-1',
    title: '斗鱼直播',
    url: 'https://www.douyu.com',
    description: '游戏直播平台',
    category: '游戏直播'
  },
  {
    id: 'live-2',
    title: '虎牙直播',
    url: 'https://www.huya.com',
    description: '游戏直播互动平台',
    category: '游戏直播'
  },
  {
    id: 'live-3',
    title: '抖音直播',
    url: 'https://live.douyin.com',
    description: '抖音直播间',
    category: '游戏直播'
  },

  // 社交媒体
  {
    id: 'social-1',
    title: '微博',
    url: 'https://weibo.com',
    description: '新浪微博',
    category: '社交媒体'
  },
  {
    id: 'social-2',
    title: '知乎',
    url: 'https://www.zhihu.com',
    description: '知识问答社区',
    category: '社交媒体'
  },
  {
    id: 'social-3',
    title: '豆瓣',
    url: 'https://www.douban.com',
    description: '文艺生活社区',
    category: '社交媒体'
  },

  // 购物网站
  {
    id: 'shop-1',
    title: '淘宝',
    url: 'https://www.taobao.com',
    description: '淘宝网购物平台',
    category: '购物网站'
  },
  {
    id: 'shop-2',
    title: '京东',
    url: 'https://www.jd.com',
    description: '京东商城',
    category: '购物网站'
  },
  {
    id: 'shop-3',
    title: '天猫',
    url: 'https://www.tmall.com',
    description: '天猫商城',
    category: '购物网站'
  },
  {
    id: 'shop-4',
    title: '拼多多',
    url: 'https://www.pinduoduo.com',
    description: '拼多多购物',
    category: '购物网站'
  },

  // 搜索引擎
  {
    id: 'search-1',
    title: '百度',
    url: 'https://www.baidu.com',
    description: '百度搜索引擎',
    category: '搜索引擎'
  },
  {
    id: 'search-2',
    title: '搜狗',
    url: 'https://www.sogou.com',
    description: '搜狗搜索',
    category: '搜索引擎'
  },
  {
    id: 'search-3',
    title: '360搜索',
    url: 'https://www.so.com',
    description: '360搜索引擎',
    category: '搜索引擎'
  },

  // 工具网站
  {
    id: 'tool-1',
    title: '百度网盘',
    url: 'https://pan.baidu.com',
    description: '百度云存储',
    category: '工具网站'
  },
  {
    id: 'tool-2',
    title: '腾讯文档',
    url: 'https://docs.qq.com',
    description: '在线协作文档',
    category: '工具网站'
  },
  {
    id: 'tool-3',
    title: '石墨文档',
    url: 'https://shimo.im',
    description: '在线协作文档',
    category: '工具网站'
  },

  // 学习教育
  {
    id: 'edu-1',
    title: '网易云课堂',
    url: 'https://study.163.com',
    description: '在线学习平台',
    category: '学习教育'
  },
  {
    id: 'edu-2',
    title: '腾讯课堂',
    url: 'https://ke.qq.com',
    description: '腾讯在线教育',
    category: '学习教育'
  },
  {
    id: 'edu-3',
    title: 'CSDN',
    url: 'https://www.csdn.net',
    description: '程序员技术社区',
    category: '学习教育'
  },

  // 音乐网站
  {
    id: 'music-1',
    title: '网易云音乐',
    url: 'https://music.163.com',
    description: '网易云音乐平台',
    category: '音乐网站'
  },
  {
    id: 'music-2',
    title: 'QQ音乐',
    url: 'https://y.qq.com',
    description: 'QQ音乐官网',
    category: '音乐网站'
  },
  {
    id: 'music-3',
    title: '酷狗音乐',
    url: 'https://www.kugou.com',
    description: '酷狗音乐网',
    category: '音乐网站'
  }
];
