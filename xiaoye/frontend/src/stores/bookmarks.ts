import { create } from 'zustand';

// 类型定义
interface Bookmark {
  id: string;
  title: string;
  url: string;
  description?: string;
  icon?: string;
  favicon?: string;
  sortOrder: number;
  isVisible: boolean;
  isQuickAccess: boolean;
  userId: string;
  categoryId?: string;
  createdAt: string;
  updatedAt: string;
}

interface Category {
  id: string;
  name: string;
  description?: string;
  icon?: string;
  color?: string;
  sortOrder: number;
  isVisible: boolean;
  isQuickAccess: boolean;
  userId: string;
  createdAt: string;
  updatedAt: string;
}

// 书签状态接口
interface BookmarkState {
  bookmarks: Bookmark[];
  categories: Category[];
  loading: boolean;
  setBookmarks: (bookmarks: Bookmark[]) => void;
  setCategories: (categories: Category[]) => void;
  setLoading: (loading: boolean) => void;
  addBookmark: (bookmark: Bookmark) => void;
  updateBookmark: (id: string, bookmark: Partial<Bookmark>) => void;
  removeBookmark: (id: string) => void;
  addCategory: (category: Category) => void;
  updateCategory: (id: string, category: Partial<Category>) => void;
  removeCategory: (id: string) => void;
}

// 书签状态管理
export const useBookmarkStore = create<BookmarkState>((set, get) => ({
  bookmarks: [],
  categories: [],
  loading: false,

  setBookmarks: (bookmarks) => set({ bookmarks }),
  setCategories: (categories) => set({ categories }),
  setLoading: (loading) => set({ loading }),

  addBookmark: (bookmark) =>
    set((state) => ({ bookmarks: [...state.bookmarks, bookmark] })),

  updateBookmark: (id, updatedBookmark) =>
    set((state) => ({
      bookmarks: state.bookmarks.map((bookmark) =>
        bookmark.id === id ? { ...bookmark, ...updatedBookmark } : bookmark
      ),
    })),

  removeBookmark: (id) =>
    set((state) => ({
      bookmarks: state.bookmarks.filter((bookmark) => bookmark.id !== id),
    })),

  addCategory: (category) =>
    set((state) => ({ categories: [...state.categories, category] })),

  updateCategory: (id, updatedCategory) =>
    set((state) => ({
      categories: state.categories.map((category) =>
        category.id === id ? { ...category, ...updatedCategory } : category
      ),
    })),

  removeCategory: (id) =>
    set((state) => ({
      categories: state.categories.filter((category) => category.id !== id),
    })),
}));

// 选择器：快捷访问书签
export const useQuickAccessBookmarks = () => {
  const bookmarks = useBookmarkStore((state) => state.bookmarks);
  return bookmarks.filter(bookmark => bookmark.isQuickAccess && bookmark.isVisible);
};

// 选择器：快捷访问分类
export const useQuickAccessCategories = () => {
  const categories = useBookmarkStore((state) => state.categories);
  return categories.filter(category => category.isQuickAccess && category.isVisible);
};

// 选择器：按分类分组的书签
export const useBookmarksByCategory = () => {
  const bookmarks = useBookmarkStore((state) => state.bookmarks);
  const categories = useBookmarkStore((state) => state.categories);

  const grouped: Record<string, { category: Category; bookmarks: Bookmark[] }> = {};

  // 初始化分类
  categories.forEach(category => {
    if (category.isVisible) {
      grouped[category.id] = {
        category,
        bookmarks: [],
      };
    }
  });

  // 分组书签
  bookmarks.forEach(bookmark => {
    if (bookmark.isVisible && bookmark.categoryId && grouped[bookmark.categoryId]) {
      grouped[bookmark.categoryId].bookmarks.push(bookmark);
    }
  });

  // 按排序顺序排序
  Object.values(grouped).forEach(group => {
    group.bookmarks.sort((a, b) => a.sortOrder - b.sortOrder);
  });

  return grouped;
};
