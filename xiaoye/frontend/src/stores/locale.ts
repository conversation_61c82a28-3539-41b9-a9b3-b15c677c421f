import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// 支持的语言类型
export type Locale = 'zh' | 'en';

// 语言状态接口
interface LocaleState {
  locale: Locale;
  setLocale: (locale: Locale) => void;
}

// 获取初始语言
function getInitialLocale(): Locale {
  // 从浏览器语言获取
  const browserLang = navigator.language.toLowerCase();
  if (browserLang.startsWith('zh')) {
    return 'zh';
  }
  return 'en';
}

// 语言状态管理
export const useLocaleStore = create<LocaleState>()(
  persist(
    (set) => ({
      locale: getInitialLocale(),
      setLocale: (locale) => set({ locale }),
    }),
    {
      name: 'locale',
    }
  )
);
