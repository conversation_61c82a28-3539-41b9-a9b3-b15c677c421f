import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// 主题模式类型
export type ThemeMode = 'light' | 'dark';

// 主题状态接口
interface ThemeState {
  mode: ThemeMode;
  setMode: (mode: ThemeMode) => void;
  toggleMode: () => void;
}

// 获取初始主题模式
function getInitialThemeMode(): ThemeMode {
  // 如果没有保存的主题，使用系统偏好
  if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
    return 'dark';
  }
  
  return 'light';
}

// 主题状态管理
export const useThemeStore = create<ThemeState>()(
  persist(
    (set, get) => ({
      mode: getInitialThemeMode(),
      setMode: (mode) => set({ mode }),
      toggleMode: () => set({ mode: get().mode === 'light' ? 'dark' : 'light' }),
    }),
    {
      name: 'theme-mode',
    }
  )
);
