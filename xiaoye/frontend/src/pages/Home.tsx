import React, { useEffect } from 'react';
import { Typo<PERSON>, Box, Grid, Button, Fab } from '@mui/material';
import { Add } from '@mui/icons-material';
import { useRecoilState, useRecoilValue } from 'recoil';
import { MainLayout } from '../layouts/MainLayout';
import { CategorySection } from '../components/CategorySection';
import { BookmarkCard } from '../components/BookmarkCard';
import {
  bookmarksState,
  categoriesState,
  bookmarksLoadingState,
  quickAccessBookmarksSelector,
  bookmarksByCategorySelector,
} from '../recoil/atoms/bookmarks';
import { bookmarkApi, categoryApi } from '../services/api';

const Home: React.FC = () => {
  const [bookmarks, setBookmarks] = useRecoilState(bookmarksState);
  const [categories, setCategories] = useRecoilState(categoriesState);
  const [loading, setLoading] = useRecoilState(bookmarksLoadingState);
  const quickAccessBookmarks = useRecoilValue(quickAccessBookmarksSelector);
  const bookmarksByCategory = useRecoilValue(bookmarksByCategorySelector);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      const [bookmarksResponse, categoriesResponse] = await Promise.all([
        bookmarkApi.getAll('user1'),
        categoryApi.getAll('user1'),
      ]);

      setBookmarks(bookmarksResponse.data);
      setCategories(categoriesResponse.data);
    } catch (error) {
      console.error('Failed to load data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddCategory = () => {
    // TODO: 打开添加分类对话框
    console.log('Add category');
  };

  const handleAddBookmark = (categoryId?: string) => {
    // TODO: 打开添加书签对话框
    console.log('Add bookmark to category:', categoryId);
  };

  if (loading) {
    return (
      <MainLayout>
        <Typography>加载中...</Typography>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <Box>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h4" component="h1">
            我的导航
          </Typography>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={handleAddCategory}
          >
            添加分类
          </Button>
        </Box>

        {/* 快捷访问区域 */}
        {quickAccessBookmarks.length > 0 && (
          <Box mb={4}>
            <Typography variant="h5" gutterBottom>
              快捷访问
            </Typography>
            <Grid container spacing={2}>
              {quickAccessBookmarks.map((bookmark) => (
                <Grid item xs={12} sm={6} md={4} lg={3} key={bookmark.id}>
                  <BookmarkCard
                    bookmark={bookmark}
                    showActions={false}
                  />
                </Grid>
              ))}
            </Grid>
          </Box>
        )}

        {/* 分类区域 */}
        {Object.values(bookmarksByCategory).map(({ category, bookmarks }) => (
          <CategorySection
            key={category.id}
            category={category}
            bookmarks={bookmarks}
            onAddBookmark={handleAddBookmark}
          />
        ))}

        {/* 添加书签浮动按钮 */}
        <Fab
          color="primary"
          aria-label="add bookmark"
          sx={{ position: 'fixed', bottom: 16, right: 16 }}
          onClick={() => handleAddBookmark()}
        >
          <Add />
        </Fab>
      </Box>
    </MainLayout>
  );
};

export default Home;
