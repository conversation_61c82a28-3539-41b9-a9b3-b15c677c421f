import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  TextField,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Divider,
} from '@mui/material';
import { Add, Delete, Launch } from '@mui/icons-material';
import { MainLayout } from '../layouts/MainLayout';
import { useThemeStore } from '../stores/theme';
import { api } from '../services/api';

interface Bookmark {
  id: string;
  title: string;
  url: string;
  description?: string;
}

export const Home: React.FC = () => {
  const { mode: themeMode } = useThemeStore();
  const [bookmarks, setBookmarks] = useState<Bookmark[]>([]);
  const [showAddForm, setShowAddForm] = useState(false);
  const [newBookmark, setNewBookmark] = useState({ title: '', url: '', description: '' });

  useEffect(() => {
    loadBookmarks();
  }, []);

  const loadBookmarks = async () => {
    try {
      const response = await api.get('/bookmarks?userId=user1');
      setBookmarks(response.data);
    } catch (error) {
      console.error('Failed to load bookmarks:', error);
    }
  };

  const handleAddBookmark = async () => {
    if (!newBookmark.title || !newBookmark.url) return;

    try {
      const response = await api.post('/bookmarks?userId=user1', newBookmark);
      setBookmarks([...bookmarks, response.data]);
      setNewBookmark({ title: '', url: '', description: '' });
      setShowAddForm(false);
    } catch (error) {
      console.error('Failed to add bookmark:', error);
    }
  };

  const handleDeleteBookmark = async (id: string) => {
    if (!confirm('确定要删除这个书签吗？')) return;
    
    try {
      await api.delete(`/bookmarks/${id}?userId=user1`);
      setBookmarks(bookmarks.filter(b => b.id !== id));
    } catch (error) {
      console.error('Failed to delete bookmark:', error);
    }
  };

  const openBookmark = (url: string) => {
    window.open(url.startsWith('http') ? url : `https://${url}`, '_blank');
  };

  return (
    <MainLayout>
      {/* 添加按钮 */}
      <Box sx={{ textAlign: 'center', mb: 4 }}>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => setShowAddForm(!showAddForm)}
          sx={{
            bgcolor: themeMode === 'dark' ? '#333' : '#1976d2',
            color: '#fff',
            '&:hover': {
              bgcolor: themeMode === 'dark' ? '#444' : '#1565c0',
            },
          }}
        >
          {showAddForm ? '取消' : '添加书签'}
        </Button>
      </Box>

      {/* 添加书签表单 */}
      {showAddForm && (
        <Box 
          sx={{ 
            mb: 4, 
            p: 3, 
            bgcolor: themeMode === 'dark' ? '#2a2a2a' : '#f9f9f9', 
            borderRadius: 1,
            border: `1px solid ${themeMode === 'dark' ? '#444' : '#e0e0e0'}`
          }}
        >
          <TextField
            fullWidth
            label="标题"
            value={newBookmark.title}
            onChange={(e) => setNewBookmark({ ...newBookmark, title: e.target.value })}
            sx={{ mb: 2 }}
            variant="outlined"
          />
          <TextField
            fullWidth
            label="网址"
            value={newBookmark.url}
            onChange={(e) => setNewBookmark({ ...newBookmark, url: e.target.value })}
            sx={{ mb: 2 }}
            variant="outlined"
          />
          <TextField
            fullWidth
            label="描述 (可选)"
            value={newBookmark.description}
            onChange={(e) => setNewBookmark({ ...newBookmark, description: e.target.value })}
            sx={{ mb: 3 }}
            variant="outlined"
          />
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
            <Button 
              variant="contained" 
              onClick={handleAddBookmark}
              disabled={!newBookmark.title || !newBookmark.url}
            >
              保存
            </Button>
            <Button 
              variant="outlined" 
              onClick={() => setShowAddForm(false)}
            >
              取消
            </Button>
          </Box>
        </Box>
      )}

      {/* 书签列表 */}
      <Box>
        {bookmarks.length === 0 ? (
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <Typography
              variant="h6"
              sx={{ 
                color: themeMode === 'dark' ? '#aaa' : '#666',
                mb: 2
              }}
            >
              还没有书签
            </Typography>
            <Typography
              variant="body2"
              sx={{ color: themeMode === 'dark' ? '#888' : '#999' }}
            >
              点击上方按钮添加第一个书签
            </Typography>
          </Box>
        ) : (
          <List sx={{ width: '100%' }}>
            {bookmarks.map((bookmark, index) => (
              <React.Fragment key={bookmark.id}>
                <ListItem
                  sx={{
                    py: 2,
                    '&:hover': {
                      bgcolor: themeMode === 'dark' ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)',
                    },
                  }}
                >
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography
                          variant="h6"
                          sx={{
                            color: themeMode === 'dark' ? '#fff' : '#333',
                            cursor: 'pointer',
                            fontSize: '1.1rem',
                            '&:hover': { 
                              color: themeMode === 'dark' ? '#64b5f6' : '#1976d2',
                              textDecoration: 'underline' 
                            },
                          }}
                          onClick={() => openBookmark(bookmark.url)}
                        >
                          {bookmark.title}
                        </Typography>
                        <IconButton
                          size="small"
                          onClick={() => openBookmark(bookmark.url)}
                          sx={{ 
                            color: themeMode === 'dark' ? '#aaa' : '#666',
                            ml: 1
                          }}
                        >
                          <Launch fontSize="small" />
                        </IconButton>
                      </Box>
                    }
                    secondary={
                      <Box sx={{ mt: 1 }}>
                        <Typography
                          variant="body2"
                          sx={{ 
                            color: themeMode === 'dark' ? '#aaa' : '#666',
                            mb: bookmark.description ? 0.5 : 0
                          }}
                        >
                          {bookmark.url}
                        </Typography>
                        {bookmark.description && (
                          <Typography
                            variant="body2"
                            sx={{ color: themeMode === 'dark' ? '#ccc' : '#888' }}
                          >
                            {bookmark.description}
                          </Typography>
                        )}
                      </Box>
                    }
                  />
                  <ListItemSecondaryAction>
                    <IconButton
                      edge="end"
                      onClick={() => handleDeleteBookmark(bookmark.id)}
                      sx={{ 
                        color: themeMode === 'dark' ? '#aaa' : '#666',
                        '&:hover': {
                          color: themeMode === 'dark' ? '#f44336' : '#d32f2f',
                        }
                      }}
                    >
                      <Delete />
                    </IconButton>
                  </ListItemSecondaryAction>
                </ListItem>
                {index < bookmarks.length - 1 && (
                  <Divider sx={{ bgcolor: themeMode === 'dark' ? '#333' : '#e0e0e0' }} />
                )}
              </React.Fragment>
            ))}
          </List>
        )}
      </Box>
    </MainLayout>
  );
};
