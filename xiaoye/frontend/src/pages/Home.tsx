import React, { useEffect } from 'react';
import { Typography, Box, Grid, Button, Fab } from '@mui/material';
import { Add } from '@mui/icons-material';
import { MainLayout } from '../layouts/MainLayout';
import { CategorySection } from '../components/CategorySection';
import { BookmarkCard } from '../components/BookmarkCard';
import {
  useBookmarkStore,
  useQuickAccessBookmarks,
  useBookmarksByCategory
} from '../stores/bookmarks';
import { api } from '../services/api';

const Home: React.FC = () => {
  const {
    bookmarks,
    categories,
    loading,
    setBookmarks,
    setCategories,
    setLoading
  } = useBookmarkStore();
  const quickAccessBookmarks = useQuickAccessBookmarks();
  const bookmarksByCategory = useBookmarksByCategory();

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      const [bookmarksResponse, categoriesResponse] = await Promise.all([
        api.get('/bookmarks?userId=user1'),
        api.get('/categories?userId=user1'),
      ]);

      setBookmarks(bookmarksResponse.data);
      setCategories(categoriesResponse.data);
    } catch (error) {
      console.error('Failed to load data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddCategory = async () => {
    const name = prompt('请输入分类名称:');
    if (!name) return;

    const description = prompt('请输入分类描述 (可选):') || '';
    const color = '#' + Math.floor(Math.random()*16777215).toString(16); // 随机颜色

    try {
      const response = await api.post('/categories?userId=user1', {
        name,
        description,
        color,
        sortOrder: categories.length + 1,
        isVisible: true,
        isQuickAccess: false
      });

      setCategories([...categories, response.data]);
    } catch (error) {
      console.error('Failed to create category:', error);
      alert('创建分类失败，请重试');
    }
  };

  const handleAddBookmark = async (categoryId?: string) => {
    const title = prompt('请输入书签标题:');
    if (!title) return;

    const url = prompt('请输入书签网址:');
    if (!url) return;

    const description = prompt('请输入书签描述 (可选):') || '';

    try {
      const response = await api.post('/bookmarks?userId=user1', {
        title,
        url,
        description,
        categoryId,
        sortOrder: bookmarks.length + 1,
        isVisible: true,
        isQuickAccess: false
      });

      setBookmarks([...bookmarks, response.data]);
    } catch (error) {
      console.error('Failed to create bookmark:', error);
      alert('创建书签失败，请重试');
    }
  };

  // 书签操作函数
  const handleEditBookmark = async (bookmark: any) => {
    const title = prompt('请输入书签标题:', bookmark.title);
    if (!title) return;

    const url = prompt('请输入书签网址:', bookmark.url);
    if (!url) return;

    const description = prompt('请输入书签描述:', bookmark.description || '');

    try {
      const response = await api.patch(`/bookmarks/${bookmark.id}?userId=user1`, {
        title,
        url,
        description
      });

      setBookmarks(bookmarks.map(b => b.id === bookmark.id ? response.data : b));
    } catch (error) {
      console.error('Failed to update bookmark:', error);
      alert('更新书签失败，请重试');
    }
  };

  const handleDeleteBookmark = async (bookmark: any) => {
    if (!confirm(`确定要删除书签 "${bookmark.title}" 吗？`)) return;

    try {
      await api.delete(`/bookmarks/${bookmark.id}?userId=user1`);
      setBookmarks(bookmarks.filter(b => b.id !== bookmark.id));
    } catch (error) {
      console.error('Failed to delete bookmark:', error);
      alert('删除书签失败，请重试');
    }
  };

  const handleToggleBookmarkQuickAccess = async (bookmark: any) => {
    try {
      const response = await api.patch(`/bookmarks/${bookmark.id}?userId=user1`, {
        isQuickAccess: !bookmark.isQuickAccess
      });

      setBookmarks(bookmarks.map(b => b.id === bookmark.id ? response.data : b));
    } catch (error) {
      console.error('Failed to toggle quick access:', error);
      alert('操作失败，请重试');
    }
  };

  // 分类操作函数
  const handleEditCategory = async (category: any) => {
    const name = prompt('请输入分类名称:', category.name);
    if (!name) return;

    const description = prompt('请输入分类描述:', category.description || '');

    try {
      const response = await api.patch(`/categories/${category.id}?userId=user1`, {
        name,
        description
      });

      setCategories(categories.map(c => c.id === category.id ? response.data : c));
    } catch (error) {
      console.error('Failed to update category:', error);
      alert('更新分类失败，请重试');
    }
  };

  const handleDeleteCategory = async (category: any) => {
    if (!confirm(`确定要删除分类 "${category.name}" 吗？这将同时删除该分类下的所有书签。`)) return;

    try {
      await api.delete(`/categories/${category.id}?userId=user1`);
      setCategories(categories.filter(c => c.id !== category.id));
      // 同时删除该分类下的书签
      setBookmarks(bookmarks.filter(b => b.categoryId !== category.id));
    } catch (error) {
      console.error('Failed to delete category:', error);
      alert('删除分类失败，请重试');
    }
  };

  if (loading) {
    return (
      <MainLayout>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
          <Typography variant="h6" color="text.secondary">
            🚀 加载中...
          </Typography>
        </Box>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <Box sx={{ width: '100%', maxWidth: '100%' }}>
        {/* 页面标题和操作区域 */}
        <Box
          display="flex"
          justifyContent="space-between"
          alignItems={{ xs: 'flex-start', sm: 'center' }}
          flexDirection={{ xs: 'column', sm: 'row' }}
          gap={{ xs: 2, sm: 0 }}
          mb={{ xs: 3, md: 4 }}
        >
          <Typography
            variant="h4"
            component="h1"
            sx={{
              fontSize: { xs: '1.75rem', sm: '2.125rem', md: '2.5rem' },
              fontWeight: 600,
              color: 'text.primary'
            }}
          >
            我的导航
          </Typography>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={handleAddCategory}
            size={{ xs: 'medium', sm: 'large' }}
            sx={{
              px: { xs: 2, sm: 3 },
              py: { xs: 1, sm: 1.5 },
              borderRadius: 2,
              boxShadow: 2,
              '&:hover': {
                boxShadow: 4,
                transform: 'translateY(-1px)'
              }
            }}
          >
            添加分类
          </Button>
        </Box>

        {/* 快捷访问区域 */}
        {quickAccessBookmarks.length > 0 && (
          <Box mb={{ xs: 4, md: 6 }}>
            <Typography
              variant="h5"
              gutterBottom
              sx={{
                fontSize: { xs: '1.25rem', sm: '1.5rem' },
                fontWeight: 500,
                mb: { xs: 2, sm: 3 },
                color: 'text.primary'
              }}
            >
              ⚡ 快捷访问
            </Typography>
            <Grid container spacing={{ xs: 2, sm: 3, md: 4 }}>
              {quickAccessBookmarks.map((bookmark) => (
                <Grid
                  item
                  xs={12}
                  sm={6}
                  md={4}
                  lg={3}
                  xl={2.4}
                  key={bookmark.id}
                >
                  <BookmarkCard
                    bookmark={bookmark}
                    showActions={false}
                  />
                </Grid>
              ))}
            </Grid>
          </Box>
        )}

        {/* 分类区域 */}
        <Box sx={{ mb: { xs: 8, sm: 10 } }}>
          {Object.values(bookmarksByCategory).length > 0 ? (
            Object.values(bookmarksByCategory).map(({ category, bookmarks }) => (
              <CategorySection
                key={category.id}
                category={category}
                bookmarks={bookmarks}
                onAddBookmark={handleAddBookmark}
                onEditCategory={handleEditCategory}
                onDeleteCategory={handleDeleteCategory}
                onEditBookmark={handleEditBookmark}
                onDeleteBookmark={handleDeleteBookmark}
                onToggleBookmarkQuickAccess={handleToggleBookmarkQuickAccess}
              />
            ))
          ) : (
            <Box
              sx={{
                textAlign: 'center',
                py: { xs: 6, sm: 8, md: 10 },
                px: 2,
                bgcolor: 'background.paper',
                borderRadius: 3,
                border: '2px dashed',
                borderColor: 'divider',
                mt: 4
              }}
            >
              <Typography
                variant="h6"
                color="text.secondary"
                gutterBottom
                sx={{ fontSize: { xs: '1rem', sm: '1.25rem' } }}
              >
                🚀 开始创建您的第一个分类
              </Typography>
              <Typography
                variant="body1"
                color="text.secondary"
                sx={{ mb: 3, fontSize: { xs: '0.875rem', sm: '1rem' } }}
              >
                点击上方"添加分类"按钮来组织您的书签
              </Typography>
              <Button
                variant="outlined"
                startIcon={<Add />}
                onClick={handleAddCategory}
                size="large"
                sx={{ borderRadius: 2 }}
              >
                添加分类
              </Button>
            </Box>
          )}
        </Box>

        {/* 添加书签浮动按钮 */}
        <Fab
          color="primary"
          aria-label="add bookmark"
          sx={{
            position: 'fixed',
            bottom: { xs: 16, sm: 24 },
            right: { xs: 16, sm: 24 },
            width: { xs: 56, sm: 64 },
            height: { xs: 56, sm: 64 },
            boxShadow: 4,
            '&:hover': {
              boxShadow: 8,
              transform: 'scale(1.05)'
            }
          }}
          onClick={() => handleAddBookmark()}
        >
          <Add sx={{ fontSize: { xs: 24, sm: 28 } }} />
        </Fab>
      </Box>
    </MainLayout>
  );
};

export default Home;
