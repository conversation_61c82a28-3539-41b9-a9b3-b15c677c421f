import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  TextField,
  Grid,
  Card,
  CardContent,
  IconButton,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Tooltip,
  Avatar,
} from '@mui/material';
import {
  Add,
  Delete,
  Launch,
  ExpandMore,
  Folder,
  FolderOpen,
  Bookmark as BookmarkIcon
} from '@mui/icons-material';
import { MainLayout } from '../layouts/MainLayout';
import { useThemeStore } from '../stores/theme';
import { useAuthStore } from '../stores/auth';
import { api } from '../services/api';
import { defaultBookmarks } from '../data/defaultBookmarks';

interface Bookmark {
  id: string;
  title: string;
  url: string;
  description?: string;
  category?: string;
  favicon?: string;
}

interface BookmarkGroup {
  category: string;
  bookmarks: Bookmark[];
}

export const Home: React.FC = () => {
  const { mode: themeMode } = useThemeStore();
  const { user, isAuthenticated } = useAuthStore();
  const [bookmarks, setBookmarks] = useState<Bookmark[]>([]);
  const [showAddForm, setShowAddForm] = useState(false);
  const [newBookmark, setNewBookmark] = useState({ title: '', url: '', description: '', category: '' });
  const [expandedGroups, setExpandedGroups] = useState<string[]>(['新闻资讯', '视频网站']); // 默认展开热门分类

  useEffect(() => {
    loadBookmarks();
  }, [isAuthenticated, user]);

  const loadBookmarks = async () => {
    try {
      if (isAuthenticated && user) {
        // 已登录用户：从数据库加载个人书签
        const response = await api.get(`/bookmarks?userId=${user.id}`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('auth-storage') ? JSON.parse(localStorage.getItem('auth-storage')!).state.token : ''}`
          }
        });
        setBookmarks(response.data);
      } else {
        // 未登录用户：使用默认书签
        const localBookmarks = localStorage.getItem('local-bookmarks');
        if (localBookmarks) {
          // 如果有本地存储的书签，合并默认书签
          const parsed = JSON.parse(localBookmarks);
          const combined = [...defaultBookmarks, ...parsed];
          setBookmarks(combined);
        } else {
          // 使用默认书签
          setBookmarks(defaultBookmarks);
        }
      }
    } catch (error) {
      console.error('Failed to load bookmarks:', error);
      // 出错时使用默认书签
      setBookmarks(defaultBookmarks);
    }
  };

  const handleAddBookmark = async () => {
    if (!newBookmark.title || !newBookmark.url) return;

    try {
      const bookmarkData = {
        ...newBookmark,
        category: newBookmark.category || '我的收藏',
        id: `user-${Date.now()}` // 生成临时ID
      };

      if (isAuthenticated && user) {
        // 已登录用户：保存到数据库
        const response = await api.post(`/bookmarks?userId=${user.id}`, bookmarkData, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('auth-storage') ? JSON.parse(localStorage.getItem('auth-storage')!).state.token : ''}`
          }
        });
        setBookmarks([...bookmarks, response.data]);
      } else {
        // 未登录用户：保存到本地存储
        const newBookmarks = [...bookmarks, bookmarkData];
        setBookmarks(newBookmarks);

        // 只保存用户添加的书签到本地存储（排除默认书签）
        const userBookmarks = newBookmarks.filter(b => b.id.startsWith('user-'));
        localStorage.setItem('local-bookmarks', JSON.stringify(userBookmarks));
      }

      setNewBookmark({ title: '', url: '', description: '', category: '' });
      setShowAddForm(false);
    } catch (error) {
      console.error('Failed to add bookmark:', error);
    }
  };

  const handleDeleteBookmark = async (id: string) => {
    // 检查是否为默认书签
    const isDefaultBookmark = defaultBookmarks.some(b => b.id === id);
    if (isDefaultBookmark && !isAuthenticated) {
      alert('默认书签无法删除，登录后可以隐藏不需要的默认书签');
      return;
    }

    if (!confirm('确定要删除这个书签吗？')) return;

    try {
      if (isAuthenticated && user) {
        // 已登录用户：从数据库删除
        await api.delete(`/bookmarks/${id}?userId=${user.id}`, {
          headers: {
            Authorization: `Bearer ${localStorage.getItem('auth-storage') ? JSON.parse(localStorage.getItem('auth-storage')!).state.token : ''}`
          }
        });
      } else {
        // 未登录用户：从本地存储删除
        const userBookmarks = bookmarks.filter(b => b.id.startsWith('user-') && b.id !== id);
        localStorage.setItem('local-bookmarks', JSON.stringify(userBookmarks));
      }

      setBookmarks(bookmarks.filter(b => b.id !== id));
    } catch (error) {
      console.error('Failed to delete bookmark:', error);
    }
  };

  const openBookmark = (url: string) => {
    window.open(url.startsWith('http') ? url : `https://${url}`, '_blank');
  };

  // 按分组整理书签
  const groupedBookmarks = React.useMemo(() => {
    const groups: BookmarkGroup[] = [];
    const groupMap = new Map<string, Bookmark[]>();

    bookmarks.forEach(bookmark => {
      const category = bookmark.category || '未分类';
      if (!groupMap.has(category)) {
        groupMap.set(category, []);
      }
      groupMap.get(category)!.push(bookmark);
    });

    // 排序：常用网站在前，其他按字母排序
    const sortedCategories = Array.from(groupMap.keys()).sort((a, b) => {
      if (a === '常用网站') return -1;
      if (b === '常用网站') return 1;
      if (a === '未分类') return 1;
      if (b === '未分类') return -1;
      return a.localeCompare(b);
    });

    sortedCategories.forEach(category => {
      groups.push({
        category,
        bookmarks: groupMap.get(category)!
      });
    });

    return groups;
  }, [bookmarks]);

  // 切换分组展开状态
  const toggleGroup = (category: string) => {
    setExpandedGroups(prev =>
      prev.includes(category)
        ? prev.filter(c => c !== category)
        : [...prev, category]
    );
  };

  // 获取网站图标
  const getFavicon = (url: string) => {
    try {
      const domain = new URL(url.startsWith('http') ? url : `https://${url}`).hostname;
      return `https://www.google.com/s2/favicons?domain=${domain}&sz=32`;
    } catch {
      return null;
    }
  };

  return (
    <MainLayout>
      {/* 页面标题和添加按钮 */}
      <Box sx={{ textAlign: 'center', mb: 4, pb: 3, borderBottom: `1px solid ${themeMode === 'dark' ? '#333' : '#e0e0e0'}` }}>
        <Typography
          variant="body2"
          sx={{
            mb: 3,
            color: themeMode === 'dark' ? '#aaa' : '#666'
          }}
        >
          {isAuthenticated
            ? `共 ${bookmarks.length} 个书签 · 云端同步`
            : `共 ${bookmarks.length} 个书签 · 本地存储 ${!isAuthenticated ? '(登录后可云端同步)' : ''}`
          }
        </Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => setShowAddForm(!showAddForm)}
          sx={{
            bgcolor: themeMode === 'dark' ? '#333' : '#1976d2',
            color: '#fff',
            px: 3,
            py: 1,
            '&:hover': {
              bgcolor: themeMode === 'dark' ? '#444' : '#1565c0',
            },
          }}
        >
          {showAddForm ? '取消添加' : '添加新书签'}
        </Button>
      </Box>

      {/* 添加书签表单 */}
      {showAddForm && (
        <Box
          sx={{
            mb: 4,
            p: 3,
            bgcolor: themeMode === 'dark' ? '#2a2a2a' : '#f9f9f9',
            borderRadius: 1,
            border: `1px solid ${themeMode === 'dark' ? '#444' : '#e0e0e0'}`
          }}
        >
          <TextField
            fullWidth
            label="标题"
            value={newBookmark.title}
            onChange={(e) => setNewBookmark({ ...newBookmark, title: e.target.value })}
            sx={{ mb: 2 }}
            variant="outlined"
          />
          <TextField
            fullWidth
            label="网址"
            value={newBookmark.url}
            onChange={(e) => setNewBookmark({ ...newBookmark, url: e.target.value })}
            sx={{ mb: 2 }}
            variant="outlined"
          />
          <TextField
            fullWidth
            label="描述 (可选)"
            value={newBookmark.description}
            onChange={(e) => setNewBookmark({ ...newBookmark, description: e.target.value })}
            sx={{ mb: 2 }}
            variant="outlined"
          />
          <TextField
            fullWidth
            label="分类 (可选)"
            value={newBookmark.category}
            onChange={(e) => setNewBookmark({ ...newBookmark, category: e.target.value })}
            placeholder="例如：常用网站、工作、学习等"
            sx={{ mb: 3 }}
            variant="outlined"
          />
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
            <Button
              variant="contained"
              onClick={handleAddBookmark}
              disabled={!newBookmark.title || !newBookmark.url}
            >
              保存
            </Button>
            <Button
              variant="outlined"
              onClick={() => {
                setShowAddForm(false);
                setNewBookmark({ title: '', url: '', description: '', category: '' });
              }}
            >
              取消
            </Button>
          </Box>
        </Box>
      )}

      {/* Safari风格的书签收藏夹 */}
      <Box>
        {bookmarks.length === 0 ? (
          <Box sx={{ textAlign: 'center', py: 6 }}>
            <Typography
              variant="h6"
              sx={{
                color: themeMode === 'dark' ? '#aaa' : '#666',
                mb: 2,
                fontSize: '1.1rem'
              }}
            >
              📚 还没有收藏任何书签
            </Typography>
            <Typography
              variant="body2"
              sx={{ color: themeMode === 'dark' ? '#888' : '#999' }}
            >
              添加您常用的网站，自动分组管理
            </Typography>
          </Box>
        ) : (
          <Box sx={{ width: '100%' }}>
            {groupedBookmarks.map((group) => (
              <Accordion
                key={group.category}
                expanded={expandedGroups.includes(group.category)}
                onChange={() => toggleGroup(group.category)}
                sx={{
                  mb: 2,
                  bgcolor: themeMode === 'dark' ? '#2a2a2a' : '#fafafa',
                  border: `1px solid ${themeMode === 'dark' ? '#333' : '#e0e0e0'}`,
                  borderRadius: '8px !important',
                  '&:before': { display: 'none' },
                  boxShadow: themeMode === 'dark'
                    ? '0 2px 8px rgba(0,0,0,0.3)'
                    : '0 2px 8px rgba(0,0,0,0.1)',
                }}
              >
                <AccordionSummary
                  expandIcon={<ExpandMore />}
                  sx={{
                    bgcolor: themeMode === 'dark' ? '#333' : '#f0f0f0',
                    borderRadius: '8px 8px 0 0',
                    minHeight: 56,
                    '&.Mui-expanded': {
                      minHeight: 56,
                    },
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                    {expandedGroups.includes(group.category) ? (
                      <FolderOpen sx={{ color: themeMode === 'dark' ? '#64b5f6' : '#1976d2' }} />
                    ) : (
                      <Folder sx={{ color: themeMode === 'dark' ? '#aaa' : '#666' }} />
                    )}
                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 500,
                        color: themeMode === 'dark' ? '#fff' : '#333',
                        flexGrow: 1,
                      }}
                    >
                      {group.category}
                    </Typography>
                    <Chip
                      label={group.bookmarks.length}
                      size="small"
                      sx={{
                        bgcolor: themeMode === 'dark' ? '#1976d2' : '#e3f2fd',
                        color: themeMode === 'dark' ? '#fff' : '#1976d2',
                        fontWeight: 600,
                      }}
                    />
                  </Box>
                </AccordionSummary>

                <AccordionDetails sx={{ p: 2 }}>
                  <Grid container spacing={2}>
                    {group.bookmarks.map((bookmark) => (
                      <Grid item xs={12} sm={6} md={4} key={bookmark.id}>
                        <Card
                          sx={{
                            height: '100%',
                            cursor: 'pointer',
                            transition: 'all 0.2s ease',
                            bgcolor: themeMode === 'dark' ? '#1e1e1e' : '#fff',
                            border: `1px solid ${themeMode === 'dark' ? '#444' : '#e0e0e0'}`,
                            '&:hover': {
                              transform: 'translateY(-2px)',
                              boxShadow: themeMode === 'dark'
                                ? '0 4px 12px rgba(0,0,0,0.4)'
                                : '0 4px 12px rgba(0,0,0,0.15)',
                              borderColor: themeMode === 'dark' ? '#64b5f6' : '#1976d2',
                            },
                          }}
                          onClick={() => openBookmark(bookmark.url)}
                        >
                          <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
                            <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1.5 }}>
                              {/* 网站图标 */}
                              <Avatar
                                src={getFavicon(bookmark.url)}
                                sx={{
                                  width: 24,
                                  height: 24,
                                  bgcolor: themeMode === 'dark' ? '#333' : '#f0f0f0',
                                }}
                              >
                                <BookmarkIcon sx={{ fontSize: 14 }} />
                              </Avatar>

                              {/* 书签信息 */}
                              <Box sx={{ flexGrow: 1, minWidth: 0 }}>
                                <Typography
                                  variant="subtitle2"
                                  sx={{
                                    fontWeight: 600,
                                    color: themeMode === 'dark' ? '#fff' : '#333',
                                    mb: 0.5,
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                    whiteSpace: 'nowrap',
                                  }}
                                >
                                  {bookmark.title}
                                </Typography>

                                <Typography
                                  variant="caption"
                                  sx={{
                                    color: themeMode === 'dark' ? '#aaa' : '#666',
                                    display: 'block',
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                    whiteSpace: 'nowrap',
                                    mb: bookmark.description ? 0.5 : 0,
                                  }}
                                >
                                  {bookmark.url}
                                </Typography>

                                {bookmark.description && (
                                  <Typography
                                    variant="caption"
                                    sx={{
                                      color: themeMode === 'dark' ? '#ccc' : '#888',
                                      fontStyle: 'italic',
                                      display: '-webkit-box',
                                      WebkitLineClamp: 2,
                                      WebkitBoxOrient: 'vertical',
                                      overflow: 'hidden',
                                    }}
                                  >
                                    {bookmark.description}
                                  </Typography>
                                )}
                              </Box>

                              {/* 操作按钮 */}
                              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                                <Tooltip title="在新标签页打开">
                                  <IconButton
                                    size="small"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      openBookmark(bookmark.url);
                                    }}
                                    sx={{
                                      color: themeMode === 'dark' ? '#64b5f6' : '#1976d2',
                                      '&:hover': {
                                        bgcolor: themeMode === 'dark' ? 'rgba(100, 181, 246, 0.1)' : 'rgba(25, 118, 210, 0.1)',
                                      }
                                    }}
                                  >
                                    <Launch sx={{ fontSize: 16 }} />
                                  </IconButton>
                                </Tooltip>

                                <Tooltip title="删除书签">
                                  <IconButton
                                    size="small"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleDeleteBookmark(bookmark.id);
                                    }}
                                    sx={{
                                      color: themeMode === 'dark' ? '#aaa' : '#666',
                                      '&:hover': {
                                        color: themeMode === 'dark' ? '#f44336' : '#d32f2f',
                                        bgcolor: themeMode === 'dark' ? 'rgba(244, 67, 54, 0.1)' : 'rgba(211, 47, 47, 0.1)',
                                      }
                                    }}
                                  >
                                    <Delete sx={{ fontSize: 16 }} />
                                  </IconButton>
                                </Tooltip>
                              </Box>
                            </Box>
                          </CardContent>
                        </Card>
                      </Grid>
                    ))}
                  </Grid>
                </AccordionDetails>
              </Accordion>
            ))}
          </Box>
        )}
      </Box>
    </MainLayout>
  );
};
