#!/bin/bash

# 快速启动脚本
echo "🚀 启动小野在线导航项目..."

# 启动后端
echo "📡 启动后端服务..."
cd backend && npm run start:dev &
BACKEND_PID=$!

# 等待后端启动
sleep 5

# 启动用户前端
echo "🌐 启动用户前端..."
cd ../frontend && npm run dev &
FRONTEND_PID=$!

# 启动管理前端
echo "⚙️ 启动管理前端..."
cd ../admin-frontend && npm run dev &
ADMIN_PID=$!

echo ""
echo "✅ 所有服务已启动!"
echo ""
echo "📊 访问地址:"
echo "   用户前端: http://localhost:5175"
echo "   管理前端: http://localhost:5176"
echo "   后端API:  http://localhost:3000/api"
echo ""
echo "💡 按 Ctrl+C 停止所有服务"

# 等待用户中断
trap 'echo ""; echo "🛑 停止所有服务..."; kill $BACKEND_PID $FRONTEND_PID $ADMIN_PID 2>/dev/null; exit' INT

# 保持脚本运行
wait
