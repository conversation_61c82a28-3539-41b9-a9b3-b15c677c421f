<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小野在线导航 - 登录测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 400px;
            margin: 100px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .login-form {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            color: #555;
            font-weight: 500;
        }
        input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 12px;
            background: #1976d2;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin-bottom: 10px;
        }
        button:hover {
            background: #1565c0;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
            font-size: 14px;
        }
        .success {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #4caf50;
        }
        .error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #f44336;
        }
        .accounts {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            font-size: 14px;
        }
        .account {
            margin-bottom: 10px;
            padding: 8px;
            background: white;
            border-radius: 3px;
            cursor: pointer;
            border: 1px solid #e0e0e0;
        }
        .account:hover {
            background: #f0f0f0;
        }
        .account strong {
            color: #1976d2;
        }
    </style>
</head>
<body>
    <div class="login-form">
        <h1>🚀 小野在线导航</h1>
        <h2 style="text-align: center; color: #666; font-size: 18px; margin-bottom: 30px;">登录测试</h2>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="email">邮箱:</label>
                <input type="email" id="email" name="email" required>
            </div>
            
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <button type="submit">登录</button>
        </form>
        
        <div class="accounts">
            <h3 style="margin-top: 0; color: #333;">测试账号:</h3>
            
            <div class="account" onclick="fillAccount('<EMAIL>', 'admin123456')">
                <strong>管理员账号</strong><br>
                邮箱: <EMAIL><br>
                密码: admin123456
            </div>
            
            <div class="account" onclick="fillAccount('<EMAIL>', 'user123456')">
                <strong>测试用户</strong><br>
                邮箱: <EMAIL><br>
                密码: user123456
            </div>
        </div>
        
        <div id="result"></div>
    </div>

    <script>
        function fillAccount(email, password) {
            document.getElementById('email').value = email;
            document.getElementById('password').value = password;
        }

        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            try {
                const response = await fetch('http://localhost:3000/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>✅ 登录成功!</strong><br>
                        用户: ${data.user.name} (${data.user.role})<br>
                        邮箱: ${data.user.email}<br>
                        <br>
                        <strong>访问链接:</strong><br>
                        ${data.user.role === 'admin' ? 
                            '<a href="http://localhost:5174" target="_blank">🔧 管理后台</a>' : 
                            '<a href="http://localhost:5173" target="_blank">🏠 用户前端</a>'
                        }
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `<strong>❌ 登录失败:</strong> ${data.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>❌ 网络错误:</strong> ${error.message}`;
            }
        });
    </script>
</body>
</html>
