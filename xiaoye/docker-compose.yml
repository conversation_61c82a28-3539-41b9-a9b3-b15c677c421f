version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: xiaoye_postgres
    environment:
      POSTGRES_DB: xiaoye_db
      POSTGRES_USER: xiaoye
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/database/init:/docker-entrypoint-initdb.d
    restart: unless-stopped

  adminer:
    image: adminer
    container_name: xiaoye_adminer
    ports:
      - "8080:8080"
    depends_on:
      - postgres
    restart: unless-stopped

volumes:
  postgres_data:
